package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 服务月账期税种核定对象 c_customer_service_period_month_tax_type_check
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Data
@ApiModel("服务月账期税种核定对象")
@Accessors(chain = true)
@TableName("c_customer_service_period_month_tax_type_check")
public class CustomerServicePeriodMonthTaxTypeCheck extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 月账期id */
    @Excel(name = "月账期id")
    @TableField("customer_service_period_month_id")
    @ApiModelProperty(value = "月账期id")
    private Long customerServicePeriodMonthId;

    /** 上报类型，1-月报，2-季报，3-年报，4-次报，5-半年报，6-无需申报 */
    @Excel(name = "上报类型，1-月报，2-季报，3-年报，4-次报，5-半年报，6-无需申报")
    @TableField("report_type")
    @ApiModelProperty(value = "上报类型，1-月报，2-季报，3-年报，4-次报，5-半年报，6-无需申报")
    private Integer reportType;

    /** 税种类型 */
    @Excel(name = "税种类型")
    @TableField("tax_type")
    @ApiModelProperty(value = "税种类型")
    private String taxType;

}
