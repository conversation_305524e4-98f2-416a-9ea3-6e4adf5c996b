package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 账期服务人员对象 c_customer_service_period_employee
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Data
@ApiModel("账期服务人员对象")
@Accessors(chain = true)
@TableName("c_customer_service_period_employee")
public class CCustomerServicePeriodEmployee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 客户服务id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "客户服务id")
    @ApiModelProperty(value = "客户服务id")
    private Long id;

    /** 账期id */
    @Excel(name = "账期id")
    @TableField("period_id")
    @ApiModelProperty(value = "账期id")
    private Long periodId;

    /** 人员类型，1-顾问，2-会计 */
    @Excel(name = "人员类型，1-顾问，2-会计")
    @TableField("period_employee_type")
    @ApiModelProperty(value = "人员类型，1-顾问，2-会计")
    private Integer periodEmployeeType;

    /** 员工id */
    @Excel(name = "员工id")
    @TableField("employee_id")
    @ApiModelProperty(value = "员工id")
    private Long employeeId;

    /** 员工名称 */
    @Excel(name = "员工名称")
    @TableField("employee_name")
    @ApiModelProperty(value = "员工名称")
    private String employeeName;

}
