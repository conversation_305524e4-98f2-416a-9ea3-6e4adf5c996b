package com.bxm.customer.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.customer.mapper.CustomerMattersNotesMapper;
import com.bxm.customer.domain.CustomerMattersNotes;
import com.bxm.customer.service.ICustomerMattersNotesService;

/**
 * 事项备忘Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class CustomerMattersNotesServiceImpl extends ServiceImpl<CustomerMattersNotesMapper, CustomerMattersNotes> implements ICustomerMattersNotesService
{
    @Autowired
    private CustomerMattersNotesMapper customerMattersNotesMapper;

    /**
     * 查询事项备忘
     * 
     * @param id 事项备忘主键
     * @return 事项备忘
     */
    @Override
    public CustomerMattersNotes selectCustomerMattersNotesById(Long id)
    {
        return customerMattersNotesMapper.selectCustomerMattersNotesById(id);
    }

    /**
     * 查询事项备忘列表
     * 
     * @param customerMattersNotes 事项备忘
     * @return 事项备忘
     */
    @Override
    public List<CustomerMattersNotes> selectCustomerMattersNotesList(CustomerMattersNotes customerMattersNotes)
    {
        return customerMattersNotesMapper.selectCustomerMattersNotesList(customerMattersNotes);
    }

    /**
     * 新增事项备忘
     * 
     * @param customerMattersNotes 事项备忘
     * @return 结果
     */
    @Override
    public int insertCustomerMattersNotes(CustomerMattersNotes customerMattersNotes)
    {
        customerMattersNotes.setCreateTime(DateUtils.getNowDate());
        return customerMattersNotesMapper.insertCustomerMattersNotes(customerMattersNotes);
    }

    /**
     * 修改事项备忘
     * 
     * @param customerMattersNotes 事项备忘
     * @return 结果
     */
    @Override
    public int updateCustomerMattersNotes(CustomerMattersNotes customerMattersNotes)
    {
        customerMattersNotes.setUpdateTime(DateUtils.getNowDate());
        return customerMattersNotesMapper.updateCustomerMattersNotes(customerMattersNotes);
    }

    /**
     * 批量删除事项备忘
     * 
     * @param ids 需要删除的事项备忘主键
     * @return 结果
     */
    @Override
    public int deleteCustomerMattersNotesByIds(Long[] ids)
    {
        return customerMattersNotesMapper.deleteCustomerMattersNotesByIds(ids);
    }

    /**
     * 删除事项备忘信息
     * 
     * @param id 事项备忘主键
     * @return 结果
     */
    @Override
    public int deleteCustomerMattersNotesById(Long id)
    {
        return customerMattersNotesMapper.deleteCustomerMattersNotesById(id);
    }

    @Override
    public String getMattersNotesByCustomerServiceIdAndItemType(Long customerServiceId, Integer itemType) {
        CustomerMattersNotes customerMattersNotes = getOne(new LambdaQueryWrapper<CustomerMattersNotes>().eq(CustomerMattersNotes::getCustomerServiceId, customerServiceId)
                .eq(CustomerMattersNotes::getItemType, itemType));
        return Objects.isNull(customerMattersNotes) || StringUtils.isEmpty(customerMattersNotes.getMattersNotes()) ? "" : customerMattersNotes.getMattersNotes();
    }

    @Override
    public void saveOrUpdateMattersNotes(Long customerServiceId, Integer itemType, String mattersNotes) {
        CustomerMattersNotes customerMattersNotes = getOne(new LambdaQueryWrapper<CustomerMattersNotes>().eq(CustomerMattersNotes::getCustomerServiceId, customerServiceId)
                .eq(CustomerMattersNotes::getItemType, itemType));
        if (Objects.isNull(customerMattersNotes)) {
            customerMattersNotes = new CustomerMattersNotes()
                    .setMattersNotes(mattersNotes)
                    .setCustomerServiceId(customerServiceId)
                    .setItemType(itemType);
            save(customerMattersNotes);
        } else {
            update(new LambdaUpdateWrapper<CustomerMattersNotes>()
                    .eq(CustomerMattersNotes::getId, customerMattersNotes.getId())
                    .set(CustomerMattersNotes::getMattersNotes, mattersNotes));
        }
    }

    @Override
    public List<CustomerMattersNotes> selectByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerMattersNotes>().eq(CustomerMattersNotes::getCustomerServiceId, customerServiceId));
    }
}
