package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.ServiceStatus;
import com.bxm.common.core.enums.ServiceWaitItemType;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.TaxType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.enums.accountingCashier.BankPaymentResult;
import com.bxm.common.core.enums.inAccount.InAccountStatus;
import com.bxm.common.core.enums.quality.QualityCheckingType;
import com.bxm.common.core.enums.repairAccount.RepairAccountStatus;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierDeliverStatusDataCount;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierMiniListDTO;
import com.bxm.customer.domain.dto.accoutingCashier.AccountingCashierWaitCreateStatisticDTO;
import com.bxm.customer.domain.dto.workBench.*;
import com.bxm.customer.domain.dto.workBench.QualityCheckingStatisticDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionStatisticDTO;
import com.bxm.customer.domain.dto.workBench.SyncItemSearchDTO;
import com.bxm.customer.domain.dto.workBench.QualityCheckingStatisticDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionMiniListDTO;
import com.bxm.customer.domain.dto.workBench.QualityExceptionStatisticDTO;
import com.bxm.customer.domain.vo.accoutingCashier.AccountingCashierMiniListSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthMiniListSearchVO;
import com.bxm.customer.domain.vo.workBench.CustomerServicePeriodMonthNoAccountingNoAdvisorVO;
import com.bxm.customer.domain.vo.workBench.InAccountDocHandoverStatisticVO;
import com.bxm.customer.domain.vo.workBench.SyncItemSearchVO;
import com.bxm.customer.domain.vo.workOrder.QualityExceptionMiniListSearchVO;
import com.bxm.customer.mapper.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class WorkBenchService {

    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private CustomerServiceInAccountMapper customerServiceInAccountMapper;

    @Autowired
    private CustomerServiceDocHandoverMapper customerServiceDocHandoverMapper;

    @Autowired
    private ICCustomerServiceWaitItemService customerServiceWaitItemService;

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private CustomerServiceCashierAccountingMapper customerServiceCashierAccountingMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private CCustomerServicePeriodEmployeeMapper customerServicePeriodEmployeeMapper;

    @Autowired
    private MaterialDeliverMapper materialDeliverMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CustomerServiceRepairAccountMapper customerServiceRepairAccountMapper;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private OpenApiSyncItemMapper openApiSyncItemMapper;

    @Autowired
    private QualityCheckingResultMapper qualityCheckingResultMapper;

    @Autowired
    private QualityCheckingItemMapper qualityCheckingItemMapper;

    @Autowired
    private ValueAddedDeliveryOrderMapper valueAddedDeliveryOrderMapper;

    public List<InAccountDocHandoverStatisticDTO> inAccountDocHandoverStatistic(InAccountDocHandoverStatisticVO vo, Long deptId) {
        if (Objects.isNull(vo.getYear())) {
            return Collections.emptyList();
        }
        List<InAccountDocHandoverStatisticDTO> result = Lists.newArrayList();
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(vo.getQueryDeptId(), vo.getDeptIds());
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        Boolean noDeptFlag = !userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds());
        Integer nowPeriod = DateUtils.getNowPeriod();
        List<PeriodCountDTO> periodsList = noDeptFlag ? Lists.newArrayList() :
                customerServicePeriodMonthMapper.selectPeriodMonthCountByYearAndUserDept(vo.getYear(), vo.getStatisticTaxType(), userDeptDTO, queryDeptIds);
        Map<Integer, PeriodCountDTO> periodsMap = ObjectUtils.isEmpty(periodsList) ? Maps.newHashMap() :
                periodsList.stream().collect(Collectors.toMap(PeriodCountDTO::getPeriod, Function.identity()));
        for (int i = 1; i <= 12; i++) {
            InAccountDocHandoverStatisticDTO dto = new InAccountDocHandoverStatisticDTO();
            Integer period = Integer.parseInt(vo.getYear() + String.format("%02d", i));
            dto.setPeriod(period);
            dto.setHasData(period <= nowPeriod);
            if (!dto.getHasData()) {
                result.add(dto);
                continue;
            }
            PeriodCountDTO periodCountDTO = periodsMap.getOrDefault(period, new PeriodCountDTO());
            Long periodCount = periodCountDTO.getCount();
            if (vo.getStatisticType() == 1) {
                if (periodCount >= 0) {
                    Long inAccountCount = periodCountDTO.getInAccountCount();
                    Long endAccountCount = periodCountDTO.getEndAccountCount();
                    dto.setServicePeriodCount(periodCount);
                    dto.setInAccountRate(periodCount == 0 ? null : getRateString(getRate(inAccountCount, periodCount)));
                    dto.setEndAccountRate(periodCount == 0 ? null : getRateString(getRate(endAccountCount, periodCount)));
                }
            } else {
                Long notInAccountCount = periodCountDTO.getNotInAccountCount();
                Long notEndAccountCount = periodCountDTO.getNotEndAccountCount();
                dto.setServicePeriodCount(periodCount);
                dto.setNotInAccountCount(notInAccountCount);
                dto.setNotEndAccountCount(notEndAccountCount);
            }
            result.add(dto);
        }
        return result;
    }

    private BigDecimal getRate(Long numerator, Long denominator) {
        return denominator == 0L ? null : new BigDecimal(numerator).divide(new BigDecimal(denominator), 2, BigDecimal.ROUND_DOWN);
    }

    private String getRateString(BigDecimal rate) {
        return Objects.isNull(rate) ? null : rate.multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
    }

    public List<ManagerStatisticDTO> managerStatistic(Integer statisticType, Long deptId) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO dept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
        dept.setDeptType(statisticType);
        List<SysDept> deptList = remoteDeptService.getDeptByUserDepts(dept).getDataThrowException();
        if (ObjectUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        deptList = deptList.stream().filter(d -> !d.getIsHeadquarters()).sorted(Comparator.comparing(SysDept::getParentId).thenComparing(SysDept::getOrderNum).thenComparing(SysDept::getDeptId)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        Integer nowPeriod = DateUtils.getNowPeriod();
        Integer prePeriod = DateUtils.getPrePeriod();
        String preMonthStartTime = DateUtils.getPreMonthStartTime();
        String preMonthEndTime = DateUtils.getPreMonthEndTime();
        List<Integer> periods = Lists.newArrayList(nowPeriod, prePeriod);
        // 查询有效户数
        List<ManagerCommonCountDTO> validCount = customerServicePeriodMonthMapper.selectValidCountByDeptTypeAndPeriods(statisticType, periods);
        // 上月重启/移除户数
        List<ManagerCommonCountDTO> restartOperCount = customerServicePeriodMonthMapper.selectOperCountByDeptTypeAndTime(statisticType, preMonthStartTime, preMonthEndTime, prePeriod, 1);
        List<ManagerCommonCountDTO> endOperCount = customerServicePeriodMonthMapper.selectOperCountByDeptTypeAndTime(statisticType, preMonthStartTime, preMonthEndTime, prePeriod, 2);
        // 上月新户
        List<ManagerCommonCountDTO> newPeriodCount = customerServicePeriodMonthMapper.selectNewPeriodCountByDeptTypeAndPeriod(statisticType, prePeriod);
        Map<Long, List<ManagerCommonCountDTO>> validCountMap = validCount.stream().collect(Collectors.groupingBy(ManagerCommonCountDTO::getDeptId));
        Map<Long, List<ManagerCommonCountDTO>> restartOperCountMap = restartOperCount.stream().collect(Collectors.groupingBy(ManagerCommonCountDTO::getDeptId));
        Map<Long, List<ManagerCommonCountDTO>> endOperCountMap = endOperCount.stream().collect(Collectors.groupingBy(ManagerCommonCountDTO::getDeptId));
        Map<Long, Long> newPeriodMap = newPeriodCount.stream().collect(Collectors.toMap(ManagerCommonCountDTO::getDeptId, ManagerCommonCountDTO::getPeriodCount));
        return deptList.stream().map(d -> {
            List<ManagerCommonCountDTO> validCountList = validCountMap.getOrDefault(d.getDeptId(), Lists.newArrayList());
            List<ManagerCommonCountDTO> restartOperCountList = restartOperCountMap.getOrDefault(d.getDeptId(), Lists.newArrayList());
            List<ManagerCommonCountDTO> endOperCountList = endOperCountMap.getOrDefault(d.getDeptId(), Lists.newArrayList());
            Map<Integer, Long> validPeriodMap = validCountList.stream().collect(Collectors.toMap(ManagerCommonCountDTO::getPeriod, ManagerCommonCountDTO::getPeriodCount));
            return ManagerStatisticDTO.builder()
                    .deptInfo(d.getDeptName())
                    .preValidCount(validPeriodMap.getOrDefault(prePeriod, 0L))
                    .thisValidCount(validPeriodMap.getOrDefault(nowPeriod, 0L))
                    .preRestartCount(ObjectUtils.isEmpty(restartOperCountList) ? 0L : restartOperCountList.get(0).getPeriodCount())
                    .preEndCount(ObjectUtils.isEmpty(endOperCountList) ? 0L : endOperCountList.get(0).getPeriodCount())
                    .preNewCount(newPeriodMap.getOrDefault(d.getDeptId(), 0L))
                    .build();
        }).collect(Collectors.toList());
    }

    public CustomerServiceCountV2DTO customerServiceStatistic(Integer statisticType, Long headerDeptId, Long deptId, String deptIds) {
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO dept = remoteDeptService.userDeptList(userId, headerDeptId).getDataThrowException();
        dept.setDeptType(statisticType);
        if (!dept.getIsAdmin() && ObjectUtils.isEmpty(dept.getDeptIds())) {
            return new CustomerServiceCountV2DTO();
        }
        Integer nowPeriod = DateUtils.getNowPeriod();
        Integer prePeriod = DateUtils.getPrePeriod();
        String preMonthStartTime = DateUtils.getPreMonthStartTime();
        String preMonthEndTime = DateUtils.getPreMonthEndTime();
        List<Integer> periods = Lists.newArrayList(nowPeriod, prePeriod);
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(deptId, deptIds);
        List<CCustomerServiceWaitItem> items = customerServiceWaitItemService.selectByDeptIdsAndItemType(dept.getDeptIds(), queryDeptIds, Arrays.asList(ServiceWaitItemType.WAIT_RE_DISPATCH.getCode(), ServiceWaitItemType.WAIT_CONFIRM_CHANGE_NAME.getCode()));
        // 查询有效户数
        List<ManagerCommonCountDTO> validCount = customerServicePeriodMonthMapper.selectValidCountByPeriods(statisticType, periods, dept, queryDeptIds);
        // 上月重启/移除户数
        List<ManagerCommonCountDTO> restartOperCount = customerServicePeriodMonthMapper.selectOperCountByTime(statisticType, preMonthStartTime, preMonthEndTime, prePeriod, 1, dept, queryDeptIds);
        List<ManagerCommonCountDTO> endOperCount = customerServicePeriodMonthMapper.selectOperCountByTime(statisticType, preMonthStartTime, preMonthEndTime, prePeriod, 2, dept, queryDeptIds);
        // 上月新户
        List<ManagerCommonCountDTO> newPeriodCount = customerServicePeriodMonthMapper.selectNewPeriodCountByPeriod(statisticType, prePeriod, dept, queryDeptIds);
        Map<Integer, Long> validCountMap = validCount.stream().collect(Collectors.toMap(ManagerCommonCountDTO::getPeriod, ManagerCommonCountDTO::getPeriodCount));
        return CustomerServiceCountV2DTO.builder()
                .waitConfirmChangeNameCount(ObjectUtils.isEmpty(items) ? 0L :
                        items.stream().filter(c -> Objects.equals(c.getItemType(), ServiceWaitItemType.WAIT_CONFIRM_CHANGE_NAME.getCode())).count())
                .excessWarningCount(customerServiceService.customerServiceWarningCount(null, headerDeptId, queryDeptIds, statisticType, null, null, null, 1, 1))
                .ticketExceptionCount(customerServicePeriodMonthIncomeService.incomeList(new CustomerServicePeriodMonthIncome()
                        .setRpaResult(2).setDeptType(statisticType).setQueryDeptIds(queryDeptIds).setCustomerEndPeriod(DateUtils.getNowPeriod()), headerDeptId, 1, 1).getTotal())
                .validCount(validCountMap.getOrDefault(prePeriod, 0L).intValue())
                .restartCount(ObjectUtils.isEmpty(restartOperCount) ? 0 : restartOperCount.get(0).getPeriodCount().intValue())
                .endServiceCount(ObjectUtils.isEmpty(endOperCount) ? 0 : endOperCount.get(0).getPeriodCount().intValue())
                .newCount(ObjectUtils.isEmpty(newPeriodCount) ? 0L : newPeriodCount.get(0).getPeriodCount())
                .thisMonthValidCount(validCountMap.getOrDefault(nowPeriod, 0L).intValue())
                .build();
    }

    public IPage<CustomerServicePeriodMonthMiniListDTO> customerServiceStatisticMiniList(CustomerServicePeriodMonthMiniListSearchVO vo, Long headerDeptId) {
        IPage<CustomerServicePeriodMonthMiniListDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO dept = remoteDeptService.userDeptList(userId, headerDeptId).getDataThrowException();
        dept.setDeptType(vo.getStatisticType());
        if (!dept.getIsAdmin() && ObjectUtils.isEmpty(dept.getDeptIds())) {
            return result;
        }
        List<Long> ids = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getTagName())) {
            if (Objects.isNull(vo.getTagIncludeFlag())) {
                vo.setTagIncludeFlag(1);
            }
            ids = vo.getType() == 1 ? businessTagRelationService.getCustomerIdsByTagNameLike(vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, headerDeptId) :
                    businessTagRelationService.getCustomerIdsByTagNameLike(vo.getTagName(), TagBusinessType.CUSTOMER_SERVICE, headerDeptId);
            if (ObjectUtils.isEmpty(ids) && vo.getTagIncludeFlag() == 1) {
                return result;
            }
        }
        Integer prePeriod = DateUtils.getPrePeriod();
        String preMonthStartTime = DateUtils.getPreMonthStartTime();
        String preMonthEndTime = DateUtils.getPreMonthEndTime();
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(vo.getDeptId(), vo.getDeptIds());
        List<CustomerServicePeriodMonthMiniListDTO> data = Lists.newArrayList();
        if (vo.getType() == 1) {
            data = customerServicePeriodMonthMapper.selectValidListByPeriod(result, vo.getStatisticType(), prePeriod, dept, queryDeptIds, vo.getTagIncludeFlag(), ids, vo);
        } else if (vo.getType() == 2) {
            data = customerServicePeriodMonthMapper.selectOperListByOperTypeAndTime(result, vo.getStatisticType(), preMonthStartTime, preMonthEndTime, prePeriod, 1, dept, queryDeptIds, vo.getTagIncludeFlag(), ids, vo);
        } else if (vo.getType() == 3) {
            data = customerServicePeriodMonthMapper.selectOperListByOperTypeAndTime(result, vo.getStatisticType(), preMonthStartTime, preMonthEndTime, prePeriod, 2, dept, queryDeptIds, vo.getTagIncludeFlag(), ids, vo);
        } else if (vo.getType() == 4) {
            data = customerServicePeriodMonthMapper.selectNewPeriodListByPeriod(result, vo.getStatisticType(), prePeriod, dept, queryDeptIds, vo.getTagIncludeFlag(), ids, vo);
        }
        if (!ObjectUtils.isEmpty(data)) {
            Map<Long, List<TagDTO>> tagsByBusinessTypeForList = vo.getType() == 1 ? businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(CustomerServicePeriodMonthMiniListDTO::getCustomerServicePeriodMonthId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD) :
                    businessTagRelationService.getTagsByBusinessTypeForList(data.stream().map(CustomerServicePeriodMonthMiniListDTO::getCustomerServiceId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE);
            List<Long> businessDeptIds = data.stream().map(CustomerServicePeriodMonthMiniListDTO::getBusinessDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, String> deptNameMap = ObjectUtils.isEmpty(businessDeptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(businessDeptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            data.forEach(d -> {
                d.setBusinessDeptName(Objects.isNull(d.getBusinessDeptId()) ? "" : deptNameMap.getOrDefault(d.getBusinessDeptId(), ""));
                List<TagDTO> tagList = vo.getType() == 1 ? tagsByBusinessTypeForList.getOrDefault(d.getCustomerServicePeriodMonthId(), Lists.newArrayList()) :
                        tagsByBusinessTypeForList.getOrDefault(d.getCustomerServiceId(), Lists.newArrayList());
                d.setTagList(tagList);
                d.setTagNames(ObjectUtils.isEmpty(tagList) ? "" : tagList.stream().map(TagDTO::getFullTagName).collect(Collectors.joining(",")));
                d.setTaxTypeStr(TaxType.getByCode(d.getTaxType()).getDesc());
            });
        }
        result.setRecords(data);
        return result;
    }

    public AccountingCashierBankStatisticDTO accountingCashierBankStatistic(Long deptId, Long queryDeptId, String deptIds) {
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        Map<Integer, Long> bankPaymentDeliverStatusDataCount = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverStatusDataCount(userDeptDTO, queryDeptIds, AccountingCashierType.FLOW.getCode(), Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()))
                .stream().collect(Collectors.toMap(AccountingCashierDeliverStatusDataCount::getDeliverStatus, AccountingCashierDeliverStatusDataCount::getDataCount));
        Integer nowPeriod = DateUtils.getNowPeriod();
        List<AccountingCashierWaitCreateStatisticDTO> waitCreateList = customerServicePeriodMonthMapper.selectBankWaitCreateList(userDeptDTO, queryDeptIds, nowPeriod);
        List<AccountingCashierWaitCreateStatisticDTO> waitDealList = customerServicePeriodMonthMapper.selectBankWaitDealList(userDeptDTO, queryDeptIds, nowPeriod);
        return AccountingCashierBankStatisticDTO.builder()
                .unOpenedCount(customerServiceService.selectNoBankCount(userDeptDTO, queryDeptIds))
                .bankPartialMissingCount(customerServicePeriodMonthMapper.selectBankPatrialMisssCount(userDeptDTO, queryDeptIds, nowPeriod))
                .waitCreateCount((long) waitCreateList.size())
                .waitResubmitCount(bankPaymentDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode(), 0L))
                .waitDeliverCount(bankPaymentDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), 0L))
                .exceptionCount(bankPaymentDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), 0L))
                .hasChangedCount(customerServiceCashierAccountingMapper.selectHasChangeCount(userDeptDTO, queryDeptIds, AccountingCashierType.FLOW.getCode()))
                .lackOfMaterialCount(customerServiceCashierAccountingMapper.selectAccountingCashierLackOfMaterialCount(userDeptDTO, queryDeptIds, AccountingCashierType.FLOW.getCode()))
                .waitSubmitCount(bankPaymentDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode(), 0L))
                .waitAdvisorCreateCount(waitCreateList.stream().filter(row -> (Objects.isNull(row.getBankDirect()) || Objects.equals(row.getBankDirect(), 2)) && (Objects.isNull(row.getReceiptStatus()) || Objects.equals(row.getReceiptStatus(), 2))).count())
                .waitReceiptCreateCount(waitCreateList.stream().filter(row -> (Objects.isNull(row.getBankDirect()) || Objects.equals(row.getBankDirect(), 2)) && Objects.equals(row.getReceiptStatus(), 1)).count())
                .waitBankCompanyCreateCount(waitCreateList.stream().filter(row -> Objects.equals(row.getBankDirect(), 1)).count())
                .waitAdvisorDealCount(waitDealList.stream().filter(row -> (Objects.isNull(row.getBankDirect()) || Objects.equals(row.getBankDirect(), 2)) && (Objects.isNull(row.getReceiptStatus()) || Objects.equals(row.getReceiptStatus(), 2))).count())
                .waitReceiptDealCount(waitDealList.stream().filter(row -> (Objects.isNull(row.getBankDirect()) || Objects.equals(row.getBankDirect(), 2)) && Objects.equals(row.getReceiptStatus(), 1)).count())
                .waitBankCompanyDealCount(waitDealList.stream().filter(row -> Objects.equals(row.getBankDirect(), 1)).count())
                .build();
    }

    public AccountingCashierInAccountStatisticDTO accountingCashierInAccountStatistic(Long deptId, Long queryDeptId, String deptIds) {
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        Map<Integer, Long> inAccountDeliverStatusDataCount = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverStatusDataCount(userDeptDTO, queryDeptIds, AccountingCashierType.INCOME.getCode(), Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode(), AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()))
                .stream().collect(Collectors.toMap(AccountingCashierDeliverStatusDataCount::getDeliverStatus, AccountingCashierDeliverStatusDataCount::getDataCount));
        Integer nowPeriod = DateUtils.getNowPeriod();
        return AccountingCashierInAccountStatisticDTO.builder()
                .waitCreateCount(customerServicePeriodMonthMapper.selectInAccountWaitCreateCount(userDeptDTO, queryDeptIds, nowPeriod))
                .waitDeliverCount(inAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), 0L))
                .waitResubmitCount(inAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode(), 0L))
                .exceptionCount(inAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), 0L))
                .hasChangedCount(customerServiceCashierAccountingMapper.selectHasChangeCount(userDeptDTO, queryDeptIds, AccountingCashierType.INCOME.getCode()))
                .lackOfMaterialCount(customerServiceCashierAccountingMapper.selectAccountingCashierLackOfMaterialCount(userDeptDTO, queryDeptIds, AccountingCashierType.INCOME.getCode()))
                .waitSubmitCount(inAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode(), 0L))
                .build();
    }

    public AccountingCashierChangeAccountStatisticDTO accountingCashierChangeAccountStatistic(Long deptId, Long queryDeptId, String deptIds) {
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        Map<Integer, Long> changeAccountDeliverStatusDataCount = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverStatusDataCount(userDeptDTO, queryDeptIds, AccountingCashierType.CHANGE.getCode(), Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()))
                .stream().collect(Collectors.toMap(AccountingCashierDeliverStatusDataCount::getDeliverStatus, AccountingCashierDeliverStatusDataCount::getDataCount));
        return AccountingCashierChangeAccountStatisticDTO.builder()
                .waitDeliverCount(changeAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), 0L))
                .exceptionCount(changeAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), 0L))
                .waitSubmitCount(changeAccountDeliverStatusDataCount.getOrDefault(AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode(), 0L))
                .build();
    }

    public IPage<AccountingCashierMiniListDTO> accountingCashierMiniList(Long deptId, AccountingCashierMiniListSearchVO vo) {
        IPage<AccountingCashierMiniListDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        List<Long> tagCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getCustomerServiceTagName())) {
            tagCustomerServiceIds = businessTagRelationService.getCustomerIdsByTagNameLike(vo.getCustomerServiceTagName(), TagBusinessType.CUSTOMER_SERVICE, deptId);
            if (vo.getCustomerServiceTagIncludeFlag() == 1 && ObjectUtils.isEmpty(tagCustomerServiceIds)) {
                return result;
            }
        }
        List<Long> batchSearchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            batchSearchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(batchSearchCustomerServiceIds)) {
                return result;
            }
        }
        List<Long> customerServicePeriodMonthIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            customerServicePeriodMonthIds = businessTagRelationService.getCustomerIdsByTagNameLike(vo.getPeriodTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
            if (vo.getPeriodTagIncludeFlag() == 1 && ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
                return result;
            }
        }
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(vo.getQueryDeptId(), vo.getDeptIds());
        List<AccountingCashierMiniListDTO> data;
        if (Objects.equals(vo.getAccountingCashierType(), AccountingCashierType.FLOW.getCode())) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            if (vo.getStatisticType() == 0) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankUnOpenList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, DateUtils.getPrePeriod());
            } else if (vo.getStatisticType() == 1) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankPatrialMisssList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, nowPeriod);
            } else if (vo.getStatisticType() == 2 || vo.getStatisticType() == 9 || vo.getStatisticType() == 10 || vo.getStatisticType() == 11) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankWaitCreateList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, nowPeriod);
            } else if (vo.getStatisticType() == 12 || vo.getStatisticType() == 13 || vo.getStatisticType() == 14) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierBankWaitDealList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, nowPeriod);
            } else if (vo.getStatisticType() == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, null, 1, null, AccountingCashierType.FLOW.getCode());
            } else if (vo.getStatisticType() == 7) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, null, null, 2, AccountingCashierType.FLOW.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(vo.getStatisticType());
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, deliverStatus, 0, null, AccountingCashierType.FLOW.getCode());
            }
        } else if (Objects.equals(vo.getAccountingCashierType(), AccountingCashierType.INCOME.getCode())) {
            Integer nowPeriod = DateUtils.getNowPeriod();
            if (vo.getStatisticType() == 2) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierInAccountWaitCreateList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, nowPeriod);
            } else if (vo.getStatisticType() == 6) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, null, 1, null, AccountingCashierType.INCOME.getCode());
            } else if (vo.getStatisticType() == 7) {
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, null, null, 2, AccountingCashierType.INCOME.getCode());
            } else {
                Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(vo.getStatisticType());
                data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, deliverStatus, 0, null, AccountingCashierType.INCOME.getCode());
            }
        } else {
            Integer deliverStatus = AccountingCashierDeliverStatus.getDeliverStatusByStatisticType(vo.getStatisticType());
            data = customerServiceCashierAccountingMapper.selectAccountingCashierDeliverList(result, vo, userDeptDTO, queryDeptIds, tagCustomerServiceIds, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, deliverStatus, 0, null, AccountingCashierType.CHANGE.getCode());
        }
        if (!ObjectUtils.isEmpty(data)) {
            fillData(data);
        }
        result.setRecords(data);
        return result;
    }

    private void fillData(List<AccountingCashierMiniListDTO> data) {
        Set<Long> deptIds = Sets.newHashSet();
        deptIds.addAll(data.stream().map(AccountingCashierMiniListDTO::getCustomerServiceAdvisorDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
        deptIds.addAll(data.stream().map(AccountingCashierMiniListDTO::getCustomerServiceAccountingDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
        deptIds.addAll(data.stream().map(AccountingCashierMiniListDTO::getPeriodAdvisorDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
        deptIds.addAll(data.stream().map(AccountingCashierMiniListDTO::getPeriodAccountingDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
        deptIds.addAll(data.stream().map(AccountingCashierMiniListDTO::getBusinessDeptId).filter(Objects::nonNull).collect(Collectors.toList()));
        List<Long> deptIdList = new ArrayList<>(deptIds);
        Map<Long, String> deptNameMap = ObjectUtils.isEmpty(deptIdList) ? Maps.newHashMap() :
                remoteDeptService.getByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIdList) ? Maps.newHashMap() :
                remoteEmployeeService.getBatchEmployeeByDeptIds(deptIdList).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        List<Long> customerServiceIds = data.stream().map(AccountingCashierMiniListDTO::getCustomerServiceId).distinct().collect(Collectors.toList());
        Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(customerServiceIds, TagBusinessType.CUSTOMER_SERVICE);
        data.forEach(d -> {
            // 银行信息
            d.setBusinessDeptName(Objects.isNull(d.getBusinessDeptId()) ? "" : deptNameMap.getOrDefault(d.getBusinessDeptId(), ""));
            d.setBankInfo((StringUtils.isEmpty(d.getBankName()) ? "" : d.getBankName()) + (StringUtils.isEmpty(d.getBankAccountNumber()) ? "" : ("（" + d.getBankAccountNumber() + "）")));
            d.setReceiptStatusStr(Objects.isNull(d.getReceiptStatus()) ? "" : (d.getReceiptStatus() == 1 ? "已托管" : "未托管"));
            d.setBankDirectStr(Objects.isNull(d.getBankDirect()) ? "" : (d.getBankDirect() == 1 ? "已开通" : "未开通"));
            // 服务顾问
            d.setCustomerServiceAdvisorDeptName(Objects.isNull(d.getCustomerServiceAdvisorDeptId()) ? "" : deptNameMap.getOrDefault(d.getCustomerServiceAdvisorDeptId(), ""));
            d.setCustomerServiceAdvisorEmployeeName(Objects.isNull(d.getCustomerServiceAdvisorDeptId()) ? "" :
                    employeeMap.getOrDefault(d.getCustomerServiceAdvisorDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            d.setCustomerServiceAdvisorInfo(StringUtils.isEmpty(d.getCustomerServiceAdvisorDeptName()) ? "" : (d.getCustomerServiceAdvisorDeptName() + (StringUtils.isEmpty(d.getCustomerServiceAdvisorEmployeeName()) ? "" : "（" + d.getCustomerServiceAdvisorEmployeeName() + "）")));
            // 服务会计
            d.setCustomerServiceAccountingDeptName(Objects.isNull(d.getCustomerServiceAccountingDeptId()) ? "" : deptNameMap.getOrDefault(d.getCustomerServiceAccountingDeptId(), ""));
            d.setCustomerServiceAccountingEmployeeName(Objects.isNull(d.getCustomerServiceAccountingDeptId()) ? "" :
                    employeeMap.getOrDefault(d.getCustomerServiceAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            d.setCustomerServiceAccountingInfo(StringUtils.isEmpty(d.getCustomerServiceAccountingDeptName()) ? "" : (d.getCustomerServiceAccountingDeptName() + (StringUtils.isEmpty(d.getCustomerServiceAccountingEmployeeName()) ? "" : "（" + d.getCustomerServiceAccountingEmployeeName() + "）")));
            // 账期顾问
            d.setPeriodAdvisorDeptName(Objects.isNull(d.getPeriodAdvisorDeptId()) ? "" : deptNameMap.getOrDefault(d.getPeriodAdvisorDeptId(), ""));
            d.setPeriodAdvisorEmployeeName(Objects.isNull(d.getPeriodAdvisorDeptId()) ? "" :
                    employeeMap.getOrDefault(d.getPeriodAdvisorDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            d.setPeriodAdvisorInfo(StringUtils.isEmpty(d.getPeriodAdvisorDeptName()) ? "" : (d.getPeriodAdvisorDeptName() + (StringUtils.isEmpty(d.getPeriodAdvisorEmployeeName()) ? "" : "（" + d.getPeriodAdvisorEmployeeName() + "）")));
            // 账期会计
            d.setPeriodAccountingDeptName(Objects.isNull(d.getPeriodAccountingDeptId()) ? "" : deptNameMap.getOrDefault(d.getPeriodAccountingDeptId(), ""));
            d.setPeriodAccountingEmployeeName(Objects.isNull(d.getPeriodAccountingDeptId()) ? "" :
                    employeeMap.getOrDefault(d.getPeriodAccountingDeptId(), Lists.newArrayList()).stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")));
            d.setPeriodAccountingInfo(StringUtils.isEmpty(d.getPeriodAccountingDeptName()) ? "" : (d.getPeriodAccountingDeptName() + (StringUtils.isEmpty(d.getPeriodAccountingEmployeeName()) ? "" : "（" + d.getPeriodAccountingEmployeeName() + "）")));

            // 银行流水
            d.setHasBankPaymentStr(Objects.isNull(d.getHasBankPayment()) ? "" : (d.getHasBankPayment() == 0 ? "否" : "是"));
            // 凭票入账
            d.setHasTicketStr(Objects.isNull(d.getHasTicket()) ? "" : (d.getHasTicket() == 0 ? "否" : "是"));
            // 交付状态
            d.setDeliverStatusStr(Objects.isNull(d.getDeliverStatus()) ? "" : AccountingCashierDeliverStatus.getByCode(d.getDeliverStatus()).getName());
            // 服务标签
            List<TagDTO> tagDTOS = tagMap.get(d.getCustomerServiceId());
            d.setCustomerServiceTags(ObjectUtils.isEmpty(tagDTOS) ? "" :
                    tagDTOS.stream().map(TagDTO::getFullTagName).collect(Collectors.joining("，")));
        });
    }

    public IPage<InAccountMiniListDTO> inAccountMiniList(Long deptId, String deptIds, Long queryDeptId, String customerName, Integer miniListType, Integer period, Integer statisticTaxType, Integer pageNum, Integer pageSize) {
        IPage<InAccountMiniListDTO> result = new Page<>(pageNum, pageSize);
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        List<InAccountMiniListDTO> data = customerServicePeriodMonthMapper.inAccountMiniList(result, userDeptDTO, queryDeptIds, customerName, miniListType, period, statisticTaxType);
        if (!ObjectUtils.isEmpty(data)) {
            data.forEach(d -> {
                d.setBankPaymentResultStr(Objects.isNull(d.getBankPaymentResult()) ? "" : BankPaymentResult.getByCode(d.getBankPaymentResult()).getDesc());
                d.setInAccountResultStr(Objects.isNull(d.getInAccountResult()) ? "" : AccountingCashierDeliverStatus.getByCode(d.getInAccountResult()).getName());
                d.setSettleAccountResultStr(Objects.isNull(d.getSettleAccountResult()) ? "" : InAccountStatus.getByCode(d.getSettleAccountResult()).getName());
            });
        }
        result.setRecords(data);
        return result;
    }

    public MaterialDeliverStatisticDTO materialDeliverStatistic(Long deptId) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return new MaterialDeliverStatisticDTO();
        }
        return materialDeliverMapper.materialDeliverStatistic(userDeptDTO);
    }

    public CustomerServiceWorkbenchV2DTO customerServiceComprehensiveStatistic(Long deptId, Long userId) {
        UserDeptDTO dept = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
        if (!dept.getIsAdmin() && ObjectUtils.isEmpty(dept.getDeptIds())) {
            return CustomerServiceWorkbenchV2DTO.builder()
                    .accountingWaitDispatchCount(0L)
                    .restartWaitDispatchCount(0L)
                    .accountingWaitReDispatchCount(0L)
                    .advisorWaitDispatchCount(0L)
                    .repairAccountWaitDispatchCount(0L)
                    .periodNoAdvisorCount(0L)
                    .periodNoAccountingCount(0L)
                    .build();
        }
        List<CCustomerServiceWaitItem> items = customerServiceWaitItemService.selectByDeptIdsAndItemType(dept.getDeptIds(), null, Arrays.asList(ServiceWaitItemType.WAIT_RE_DISPATCH.getCode()));
        return CustomerServiceWorkbenchV2DTO.builder()
                .accountingWaitDispatchCount(customerServiceMapper.selectItemCount(dept, 1))
                .restartWaitDispatchCount(customerServiceMapper.selectItemCount(dept, 2))
                .accountingWaitReDispatchCount(ObjectUtils.isEmpty(items) ? 0L : (long) items.size())
                .advisorWaitDispatchCount(customerServiceMapper.selectItemCount(dept, 3))
                .repairAccountWaitDispatchCount(customerServiceRepairAccountMapper.countWaitDispatchByDeptAndStatus(dept, RepairAccountStatus.NEED_GIVE.getCode()))
                .periodNoAdvisorCount(customerServicePeriodMonthMapper.selectNoAdvisorCount(dept))
                .periodNoAccountingCount(customerServicePeriodMonthMapper.selectNoAccountingCount(dept))
                .build();
    }

    public IPage<CustomerServiceWaitItemDTO> customerServiceWaitDispatchList(Integer itemType, String keyWord, Long deptId, String tagName, Integer tagIncludeFlag, Integer taxType, Integer pageNum, Integer pageSize) {
        return customerServiceService.customerServiceWaitDispatchList(itemType, keyWord, deptId, tagName, tagIncludeFlag, taxType, pageNum, pageSize);
    }

    public IPage<SyncItemSearchDTO> syncItemPageList(Long deptId, SyncItemSearchVO vo) {
        IPage<SyncItemSearchDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(vo.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        List<Long> batchSearchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            batchSearchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(batchSearchCustomerServiceIds)) {
                return result;
            }
        }
        List<Long> customerServicePeriodMonthIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getPeriodTagName())) {
            customerServicePeriodMonthIds = businessTagRelationService.getCustomerIdsByTagNameLike(vo.getPeriodTagName(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, deptId);
            if (vo.getPeriodTagIncludeFlag() == 1 && ObjectUtils.isEmpty(customerServicePeriodMonthIds)) {
                return result;
            }
        }
        vo.setPeriod(DateUtils.getPrePeriod());
        vo.setReportPeriod(DateUtils.periodToYeaMonth(DateUtils.getNextPeriod(vo.getPeriod())));
        vo.setPeriodStr(DateUtils.periodToYeaMonth(vo.getPeriod()));
        vo.setNowPeriod(DateUtils.getNowPeriod());
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(vo.getDeptId(), vo.getDeptIds());
        Integer month = vo.getNowPeriod() % 100;
        List<String> reportTypes = null;
        if (Lists.newArrayList(2, 3, 5, 6, 8, 9, 11, 12).contains(month)) {
            reportTypes = Lists.newArrayList("月", "次");
        } else if (month == 4 || month == 10) {
            reportTypes = Lists.newArrayList("月", "次", "季");
        } else if (month == 7) {
            reportTypes = Lists.newArrayList("月", "次", "季", "半年");
        }
        List<SyncItemSearchDTO> data = openApiSyncItemMapper.selectSyncItemPageList(result, vo, userDeptDTO, batchSearchCustomerServiceIds, customerServicePeriodMonthIds, queryDeptIds, reportTypes);
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> periodIds = data.stream().map(SyncItemSearchDTO::getCustomerServicePeriodMonthId).distinct().collect(Collectors.toList());
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(periodIds, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            List<Long> deptIds = Lists.newArrayList();
            deptIds.addAll(data.stream().map(SyncItemSearchDTO::getAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deptIds.addAll(data.stream().map(SyncItemSearchDTO::getAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException()
                            .stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            Map<Long, String> deptMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
            data.forEach(d -> {
                List<TagDTO> tagDTOS = tagMap.get(d.getCustomerServicePeriodMonthId());
                List<SysEmployee> advisorEmployee = Objects.isNull(d.getAdvisorDeptId()) ? Lists.newArrayList() : employeeMap.get(d.getAdvisorDeptId());
                String advisorEmployeeName = ObjectUtils.isEmpty(advisorEmployee) ? "" :
                        advisorEmployee.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，"));
                List<SysEmployee> accountingEmployee = Objects.isNull(d.getAccountingDeptId()) ? Lists.newArrayList() : employeeMap.get(d.getAccountingDeptId());
                String accountingEmployeeName = ObjectUtils.isEmpty(accountingEmployee) ? "" :
                        accountingEmployee.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，"));
                String advisorDeptName = deptMap.getOrDefault(d.getAdvisorDeptId(), "");
                String accountingDeptName = deptMap.getOrDefault(d.getAccountingDeptId(), "");
                d.setTaxTypeStr(Objects.isNull(d.getTaxType()) ? "" : TaxType.getByCode(d.getTaxType()).getDesc());
                d.setTagNames(ObjectUtils.isEmpty(tagDTOS) ? "" : tagDTOS.stream().map(TagDTO::getFullTagName).collect(Collectors.joining("，")));
                d.setCategoryItemName(d.getCategoryName() + (StringUtils.isEmpty(d.getItemName()) ? "" : ("-" + d.getItemName())));
                d.setAdvisorInfo(advisorDeptName + (StringUtils.isEmpty(advisorEmployeeName) ? "" : "（" + advisorEmployeeName + "）"));
                d.setAccountingInfo(accountingDeptName + (StringUtils.isEmpty(accountingEmployeeName) ? "" : "（" + accountingEmployeeName + "）"));
                d.setReportPeriodStartEnd(StringUtils.isEmpty(d.getReportPeriodStart()) || StringUtils.isEmpty(d.getReportPeriodEnd()) ? "" :
                        (d.getReportPeriodStart() + " ~ " + d.getReportPeriodEnd()));
            });
        }
        result.setRecords(data);
        return result;
    }

    public QualityCheckingStatisticDTO qualityCheckingStatistic(Long deptId, Long queryDeptId, String deptIds) {
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return new QualityCheckingStatisticDTO();
        }
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(queryDeptId, deptIds);
        List<QualityExceptionStatisticDTO> qualityExceptionStatistic = qualityCheckingResultMapper.selectExceptionStatistic(userDept, queryDeptIds);
        Map<Integer, Long> statisticMap = qualityExceptionStatistic.stream().collect(Collectors.toMap(QualityExceptionStatisticDTO::getQualityCheckingType, QualityExceptionStatisticDTO::getExceptionCount));
        return QualityCheckingStatisticDTO.builder()
                .exceptionAccountingCount(statisticMap.getOrDefault(QualityCheckingType.ACCOUNT_PROBLEM.getCode(), 0L))
                .exceptionRiskCount(statisticMap.getOrDefault(QualityCheckingType.RISK_WARNING.getCode(), 0L))
                .build();
    }

    public IPage<QualityExceptionMiniListDTO> qualityExceptionMiniList(Long deptId, QualityExceptionMiniListSearchVO vo) {
        IPage<QualityExceptionMiniListDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return result;
        }
        List<Long> batchSearchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            batchSearchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(batchSearchCustomerServiceIds)) {
                return result;
            }
        }
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(vo.getQueryDeptId(), vo.getDeptIds());
        List<QualityExceptionMiniListDTO> data = qualityCheckingResultMapper.selectExceptionMiniList(result, vo, userDept, queryDeptIds, batchSearchCustomerServiceIds);
        if (!ObjectUtils.isEmpty(data)) {
            fillQualityExceptionMiniListData(data);
        }
        result.setRecords(data);
        return result;
    }

    public void fillQualityExceptionMiniListData(List<QualityExceptionMiniListDTO> data) {
        if (ObjectUtils.isEmpty(data)) {
            return;
        }
        List<Long> qualityCheckingItemIds = data.stream().map(QualityExceptionMiniListDTO::getQualityCheckingItemId).distinct().collect(Collectors.toList());
        Map<Long, QualityCheckingItem> itemMap = qualityCheckingItemMapper.selectBatchIds(qualityCheckingItemIds)
                        .stream().collect(Collectors.toMap(QualityCheckingItem::getId, Function.identity()));
        List<Long> deptIds = Lists.newArrayList();
        deptIds.addAll(data.stream().map(QualityExceptionMiniListDTO::getAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        deptIds.addAll(data.stream().map(QualityExceptionMiniListDTO::getAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, String> deptMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        data.forEach(row -> {
            QualityCheckingItem qualityCheckingItem = itemMap.get(row.getQualityCheckingItemId());
            row.setQualityCheckingItemName(Objects.isNull(qualityCheckingItem) ? "" : qualityCheckingItem.getItemName());
            row.setPeriodStr(DateUtils.periodToYeaMonth(row.getPeriod()));
            String advisorDeptName = deptMap.getOrDefault(row.getAdvisorDeptId(), "");
            String accountingDeptName = deptMap.getOrDefault(row.getAccountingDeptId(), "");
            List<SysEmployee> advisorEmployees = Objects.isNull(row.getAdvisorDeptId()) ? Lists.newArrayList() : employeeMap.getOrDefault(row.getAdvisorDeptId(), Lists.newArrayList());
            List<SysEmployee> accountingEmployees = Objects.isNull(row.getAccountingDeptId()) ? Lists.newArrayList() : employeeMap.getOrDefault(row.getAccountingDeptId(), Lists.newArrayList());
            row.setAdvisorInfo(advisorDeptName + (ObjectUtils.isEmpty(advisorEmployees) ? "" : ("（" + advisorEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")) + "）")));
            row.setAccountingInfo(accountingDeptName + (ObjectUtils.isEmpty(accountingEmployees) ? "" : ("（" + accountingEmployees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")) + "）")));
        });
    }

    public IPage<CustomerServicePeriodMonthSimpleDTO> periodNoAccountingNoAdvisorPageList(Long deptId, CustomerServicePeriodMonthNoAccountingNoAdvisorVO vo) {
        IPage<CustomerServicePeriodMonthSimpleDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(vo.getUserId(), deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        List<Long> batchSearchCustomerServiceIds = Lists.newArrayList();
        if (!StringUtils.isEmpty(vo.getBatchNo())) {
            batchSearchCustomerServiceIds = redisService.getLargeCacheList(CacheConstants.CUSTOMER_BATCH_SEARCH_RESULT_LIST + vo.getBatchNo(), 500);
            if (ObjectUtils.isEmpty(batchSearchCustomerServiceIds)) {
                return result;
            }
        }
        List<CustomerServicePeriodMonthSimpleDTO> data = customerServicePeriodMonthMapper.periodNoAccountingNoAdvisorPageList(result, userDeptDTO, vo, batchSearchCustomerServiceIds);
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> deptIds = Lists.newArrayList();
            if (vo.getType() == 1) {
                deptIds.addAll(data.stream().map(CustomerServicePeriodMonthSimpleDTO::getCustomerServiceAccountingDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            } else {
                deptIds.addAll(data.stream().map(CustomerServicePeriodMonthSimpleDTO::getCustomerServiceAdvisorDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            }
            deptIds.addAll(data.stream().map(CustomerServicePeriodMonthSimpleDTO::getAccountingTopDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            Map<Long, SysDept> deptMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteDeptService.getByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(deptIds) ? Maps.newHashMap() :
                    remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException().stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
            data.forEach(d -> {
                d.setPeriodStr(DateUtils.periodToYeaMonth(d.getPeriod()));
                d.setServiceStatusStr(ServiceStatus.getServiceStatusByCode(d.getServiceStatus()).getDesc());
                if (vo.getType() == 1) {
                    SysDept dept = deptMap.get(d.getCustomerServiceAccountingDeptId());
                    String deptName = Objects.isNull(dept) ? "" : dept.getDeptName();
                    List<SysEmployee> employees = employeeMap.get(d.getCustomerServiceAccountingDeptId());
                    d.setCustomerServiceAccountingDeptInfo(deptName + (ObjectUtils.isEmpty(employees) ? "" : ("（" + employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")) + "）")));
                } else {
                    SysDept dept = deptMap.get(d.getCustomerServiceAdvisorDeptId());
                    String deptName = Objects.isNull(dept) ? "" : dept.getDeptName();
                    List<SysEmployee> employees = employeeMap.get(d.getCustomerServiceAdvisorDeptId());
                    d.setCustomerServiceAdvisorDeptInfo(deptName + (ObjectUtils.isEmpty(employees) ? "" : ("（" + employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("，")) + "）")));
                }
                if (!Objects.isNull(d.getAccountingTopDeptId())) {
                    SysDept accountingTopDept = deptMap.get(d.getAccountingTopDeptId());
                    if (!Objects.isNull(accountingTopDept)) {
                        d.setAccountingLeaderDeptId(accountingTopDept.getParentId());
                    }
                }
            });
        }
        result.setRecords(data);
        return result;
    }

    public ValueAddedDeliverStatisticDTO valueAddedDeliverStatistic(Long deptId, String deptIds) {
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return new ValueAddedDeliverStatisticDTO();
        }
        LambdaQueryWrapper<ValueAddedDeliveryOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(null, deptIds);
        // START 数据权限相关，业务公司按发起组取，会计工厂按承接组取
        if (userDept.getDeptType() == 1) {
            if (!ObjectUtils.isEmpty(queryDeptIds)) {
                wrapper.in(ValueAddedDeliveryOrder::getInitiateDeptId, queryDeptIds);
            }
            if (!userDept.getIsAdmin()) {
                wrapper.in(ValueAddedDeliveryOrder::getInitiateDeptId, userDept.getDeptIds());
            }
        } else {
            if (!ObjectUtils.isEmpty(queryDeptIds)) {
                wrapper.and(queryWrapper -> queryWrapper
                        .in(ValueAddedDeliveryOrder::getAccountingDeptId, queryDeptIds)
                        .or()
                        .in(ValueAddedDeliveryOrder::getAccountingTopDeptId, queryDeptIds));
            }
            if (!userDept.getIsAdmin()) {
                wrapper.and(queryWrapper -> queryWrapper
                        .in(ValueAddedDeliveryOrder::getAccountingDeptId, userDept.getDeptIds())
                        .or()
                        .in(ValueAddedDeliveryOrder::getAccountingTopDeptId, userDept.getDeptIds()));
            }
        }
        wrapper.select(ValueAddedDeliveryOrder::getId, ValueAddedDeliveryOrder::getAccountingDeptId);
        List<ValueAddedDeliveryOrder> list = valueAddedDeliveryOrderMapper.selectList(wrapper);
        return ValueAddedDeliverStatisticDTO.builder()
                .waitDispatchCount(list.stream().filter(d -> Objects.isNull(d.getAccountingDeptId())).count())
                .build();
    }
}
