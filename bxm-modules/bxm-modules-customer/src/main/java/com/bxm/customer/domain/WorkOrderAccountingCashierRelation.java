package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 工单账务交付单关联对象 c_work_order_accounting_cashier_relation
 * 
 * <AUTHOR>
 * @date 2024-12-29
 */
@Data
@ApiModel("工单账务交付单关联对象")
@Accessors(chain = true)
@TableName("c_work_order_accounting_cashier_relation")
public class WorkOrderAccountingCashierRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 工单id */
    @Excel(name = "工单id")
    @TableField("work_order_id")
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /** 账务交付单id */
    @Excel(name = "账务交付单id")
    @TableField("cashier_accounting_id")
    @ApiModelProperty(value = "账务交付单id")
    private Long cashierAccountingId;

    /** 交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提 */
    @Excel(name = "交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提")
    @TableField("cashier_accounting_deliver_status")
    @ApiModelProperty(value = "交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提")
    private Integer cashierAccountingDeliverStatus;

    /** 交付结果，1-正常，2-无账务，3-无需交付，4-异常 */
    @Excel(name = "交付结果，1-正常，2-无账务，3-无需交付，4-异常")
    @TableField("cashier_accounting_deliver_result")
    @ApiModelProperty(value = "交付结果，1-正常，2-无账务，3-无需交付，4-异常")
    private Integer cashierAccountingDeliverResult;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
