package com.bxm.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.TaxType;
import com.bxm.common.core.utils.LogUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.logstring.FieldReference;
import com.bxm.common.core.utils.logstring.LogStringConfig;
import com.bxm.common.core.utils.logstring.OutputFormat;
import com.bxm.common.core.utils.uuid.IdUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.log.service.AsyncLogService;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.config.BatchOperationConfig;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.constants.ValueAddedConstants;
import com.bxm.customer.domain.dto.DispatchRequestDTO;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingResultDTO;
import com.bxm.customer.domain.dto.valueAdded.DispatchErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.DispatchResultDTO;
import com.bxm.customer.domain.enums.*;
import com.bxm.customer.domain.enums.AccountingMethodEnum;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.vo.repairAccount.OperateUserInfoDTO;
import com.bxm.customer.domain.vo.valueAdded.*;
import com.bxm.customer.mapper.ValueAddedDeliveryOrderMapper;
import com.bxm.customer.service.*;
import com.bxm.customer.service.strategy.ValueAddedEmployeeStrategyFactory;
import com.bxm.customer.service.strategy.ValueAddedEmployeeUpsertStrategy;
import com.bxm.customer.utils.BeanUtils;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.file.api.domain.ValueAddedFileDTO;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.BusinessLogDTO;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 增值交付单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedDeliveryOrderServiceImpl extends ServiceImpl<ValueAddedDeliveryOrderMapper, ValueAddedDeliveryOrder> implements IValueAddedDeliveryOrderService {


    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationConfig batchOperationConfig;

    @Autowired
    private DictConversionService dictConversionService;

    @Autowired
    private ValueAddedDeliveryOrderStateMachineManager valueAddedDeliveryOrderStateMachineManager;

    @Autowired
    private IValueAddedEmployeeService valueAddedEmployeeService;

    @Autowired
    private ValueAddedEmployeeStrategyFactory strategyFactory;

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Autowired
    private IValueAddedStockService valueAddedStockService;

    @Autowired
    private AsyncLogService asyncLogService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private CuCustomerServiceService cuCustomerServiceService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ICustomerServiceDocHandoverService customerServiceDocHandoverService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedDeliveryOrder upsert(DeliveryOrderUpsertReq orderVO) {
        try {
            log.info("Starting upsert operation for delivery order: {}", orderVO.getCustomerName());

            // 执行基础校验
            validateOrderVO(orderVO);

            // VO转换为DO
            ValueAddedDeliveryOrder order = new ValueAddedDeliveryOrder();
            BeanUtils.copyProperties(orderVO, order);

            // 通过creditCode自动填充customerId，完全忽略前端传的customerId
            if (StringUtils.isNotEmpty(orderVO.getCreditCode())) {
                try {
                    List<CCustomerService> customerServices = cuCustomerServiceService.getCustomersByCreditCode(orderVO.getCreditCode());
                    if (customerServices != null && !customerServices.isEmpty()) {
                        // 如果有多条记录，取ID最大的那条记录的customerId
                        CCustomerService targetCustomer = customerServices.stream()
                                .max(Comparator.comparing(CCustomerService::getId))
                                .orElse(null);
                        if (targetCustomer != null) {
                            order.setCustomerId(targetCustomer.getId());
                        }
                    }
                } catch (Exception e) {
                }
            }

            // 生成交付单标题
            String title = generateTitle(order);
            order.setTitle(title);

            // 查找现有记录
            ValueAddedDeliveryOrder existingOrder = findExistingOrder(order);

            ValueAddedDeliveryOrder resultOrder;
            // 在新增时校验特定itemCode的人员明细数据
            validateEmployeeDataForPersonalTaxItems(order);
            if (existingOrder != null) {
                // 更新现有记录
                updateExistingOrder(existingOrder, order);
                String updatedTitle = generateTitle(existingOrder);
                existingOrder.setTitle(updatedTitle);
                updateById(existingOrder);
                resultOrder = existingOrder;
                // 记录更新操作日志
                saveUpsertBusinessLog(resultOrder.getId(), "更新增值交付单", orderVO);
            } else {
                // 创建新记录
                if (StringUtils.isEmpty(order.getStatus())) {
                    order.setStatus(ValueAddedDeliveryOrderStatus.getDefaultStatus().getCode()); // 使用枚举设置默认状态
                }
                order.setIsDel(false);
                Long userId = SecurityUtils.getUserId();
                order.setCreateUid(userId);
                order.setCreateBy(SecurityUtils.getUsername());
                order.setUpdateBy(SecurityUtils.getUsername());
                save(order);
                resultOrder = order;
                // 记录新增操作日志
                saveUpsertBusinessLog(resultOrder.getId(), "新增增值交付单", orderVO);
            }

            // 处理国税账号和个税账号信息
            processAccountInformation(orderVO, resultOrder.getDeliveryOrderNo());

            // 删除老的交付文件
            deleteOldDeliveryFiles(resultOrder.getDeliveryOrderNo());
            // 保存交付文件
            saveDeliveryFiles(orderVO.getDeliveryFiles(), resultOrder.getDeliveryOrderNo());

            return resultOrder;

        } catch (Exception e) {
            log.error("Failed to upsert delivery order for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("保存增值交付单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除老的交付文件
     *
     * @param deliveryOrderNo
     */
    private void deleteOldDeliveryFiles(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return;
        }
        List<ValueAddedFile> oldFiles = valueAddedFileService.getByDeliveryOrderNoAndFileTypes(deliveryOrderNo, ValueAddedFileType.DELIVERY_ATTACHMENT.getCode());
        if (ObjectUtils.isEmpty(oldFiles)) {
            return;
        }
        valueAddedFileService.updateBatchById(oldFiles.stream().map(file -> new ValueAddedFile().setId(file.getId()).setIsDel(true)).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueAddedDeliveryOrder submitToPendingDelivery(DeliveryOrderUpsertReq orderVO) {
        try {
            // 1. 校验是否存在同类型的交付单
            validateNoDuplicateDeliveryOrder(orderVO);

            // 2. 先调用upsert接口保存数据
            ValueAddedDeliveryOrder savedOrder = upsert(orderVO);

            // 3. 校验当前状态是否可以转换到SUBMITTED_PENDING_DELIVERY
            String currentStatus = savedOrder.getStatus();
            ValueAddedDeliveryOrderStatus currentStatusEnum = ValueAddedDeliveryOrderStatus.getByCode(currentStatus);
            ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY;

            // 4. 检查是否已经是目标状态
            if (targetStatus.getCode().equals(currentStatus)) {
                log.info("Delivery order {} is already in SUBMITTED_PENDING_DELIVERY status", savedOrder.getDeliveryOrderNo());
                return savedOrder;
            }

            // 5. 保存状态更新到数据库
            savedOrder.setStatus(targetStatus.getCode());

            // 如果目标状态是已提交待交付，设置发起时间
            if (ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(targetStatus.getCode())) {
                savedOrder.setInitiateTime(new Date());
            }

            boolean updateResult = updateById(savedOrder);
            if (!updateResult) {
                log.error("Database update failed for order: {}, transaction will be rolled back", savedOrder.getDeliveryOrderNo());
                throw new RuntimeException("状态更新失败，事务已回滚");
            }
            return savedOrder;
        } catch (IllegalArgumentException e) {
            log.warn("Submit to pending delivery validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Failed to submit delivery order to pending delivery for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("提交到待交付状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo) {
        if (StringUtils.isEmpty(deliveryOrderNo)) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getDeliveryOrderNo, deliveryOrderNo.trim())
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    @Override
    public List<ValueAddedDeliveryOrder> listByDeliveryOrderNos(List<String> deliveryOrderNos) {
        if (deliveryOrderNos == null || deliveryOrderNos.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤空值并去重
        List<String> validOrderNos = deliveryOrderNos.stream()
                .filter(StringUtils::isNotEmpty)
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());

        if (validOrderNos.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, validOrderNos)
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return list(queryWrapper);
    }

    @Override
    public DeliveryOrderVO getDeliveryOrderVO(String deliveryOrderNo) {
        try {
            log.info("Getting delivery order VO for order number: {}", deliveryOrderNo);

            // 获取基础交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                log.warn("Delivery order not found: {}", deliveryOrderNo);
                return null;
            }
            DeliveryOrderVO vo = buildBaseDeliveryOrderVO(order);

            // 获取并设置扩展数据
            setExtendedData(vo, deliveryOrderNo, order.getValueAddedItemTypeId());

            fillAccountingNameInfo(Arrays.asList(vo));
            fillInitiateDeptNameInfo(Arrays.asList(vo));
            log.info("Successfully built delivery order VO for: {}", deliveryOrderNo);
            return vo;

        } catch (Exception e) {
            log.error("Failed to get delivery order VO for: {}", deliveryOrderNo, e);
            throw new RuntimeException("获取交付单详细信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType) {
        if (customerId == null || valueAddedItemType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedDeliveryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedDeliveryOrder::getCustomerId, customerId)
                .eq(ValueAddedDeliveryOrder::getValueAddedItemTypeId, valueAddedItemType)
                .eq(ValueAddedDeliveryOrder::getIsDel, false);

        return getOne(queryWrapper);
    }

    /**
     * 校验是否存在同类型的交付单
     * 条件：同类型的交付单 itemTypeId，状态 not in(已完成、已扣款)，账期范围在orderVO中的，且税号或信用代码匹配
     * 如果存在则抛出异常
     *
     * @param orderVO 交付单请求对象
     */
    private void validateNoDuplicateDeliveryOrder(DeliveryOrderUpsertReq orderVO) {
        try {
            log.info("Validating duplicate delivery order for customer: {}, itemType: {}",
                    orderVO.getCustomerName(), orderVO.getValueAddedItemTypeId());

            // 构建查询条件
            LambdaQueryWrapper<ValueAddedDeliveryOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedDeliveryOrder::getIsDel, false)
                    // 同类型的交付单
                    .eq(ValueAddedDeliveryOrder::getValueAddedItemTypeId, orderVO.getValueAddedItemTypeId())
                    // 状态不是已完成和已扣款
                    .notIn(ValueAddedDeliveryOrder::getStatus,
                            ValueAddedDeliveryOrderStatus.COMPLETED.getCode(),
                            ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode());

            // 税号或信用代码匹配
            if (StringUtils.isNotEmpty(orderVO.getTaxNo()) || StringUtils.isNotEmpty(orderVO.getCreditCode())) {
                wrapper.and(w -> {
                    if (StringUtils.isNotEmpty(orderVO.getTaxNo())) {
                        w.eq(ValueAddedDeliveryOrder::getTaxNo, orderVO.getTaxNo());
                    }
                    if (StringUtils.isNotEmpty(orderVO.getCreditCode())) {
                        if (StringUtils.isNotEmpty(orderVO.getTaxNo())) {
                            w.or();
                        }
                        w.eq(ValueAddedDeliveryOrder::getCreditCode, orderVO.getCreditCode());
                    }
                });
            }

            // 账期范围在orderVO中的：查询账期范围与orderVO账期范围有交集
            if (orderVO.getAccountingPeriodStart() != null && orderVO.getAccountingPeriodEnd() != null) {
                // 查询账期范围与orderVO账期范围有交集的记录
                // 条件：orderVO开始 <= 记录结束 AND orderVO结束 >= 记录开始
                wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, orderVO.getAccountingPeriodEnd())
                        .ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, orderVO.getAccountingPeriodStart());
            } else if (orderVO.getAccountingPeriodStart() != null) {
                // 只有开始时间：记录的结束时间 >= orderVO开始时间
                wrapper.ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, orderVO.getAccountingPeriodStart());
            } else if (orderVO.getAccountingPeriodEnd() != null) {
                // 只有结束时间：记录的开始时间 <= orderVO结束时间
                wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, orderVO.getAccountingPeriodEnd());
            }

            // 排除当前交付单（如果是更新操作）
            if (StringUtils.isNotEmpty(orderVO.getDeliveryOrderNo())) {
                wrapper.ne(ValueAddedDeliveryOrder::getDeliveryOrderNo, orderVO.getDeliveryOrderNo());
            }

            // 查询是否存在符合条件的记录
            List<ValueAddedDeliveryOrder> existingOrders = this.list(wrapper);

            if (!existingOrders.isEmpty()) {
                ValueAddedDeliveryOrder existingOrder = existingOrders.get(0);
                String errorMsg = String.format("存在同类型的交付单，不允许提交。已存在交付单：%s（客户：%s，状态：%s）",
                        existingOrder.getDeliveryOrderNo(),
                        existingOrder.getCustomerName(),
                        ValueAddedDeliveryOrderStatus.getByCode(existingOrder.getStatus()).getDescription());
                log.warn("Duplicate delivery order found: {}", errorMsg);
                throw new IllegalArgumentException(errorMsg);
            }

        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to validate duplicate delivery order for customer: {}", orderVO.getCustomerName(), e);
            throw new RuntimeException("校验重复交付单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建查询条件包装器
     *
     * @param q        查询参数
     * @param userDept 用户部门信息
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<ValueAddedDeliveryOrder> buildQueryWrapper(DeliveryOrderQuery q, UserDeptDTO userDept) {
        // 构建动态查询条件
        LambdaQueryWrapper<ValueAddedDeliveryOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ValueAddedDeliveryOrder::getIsDel, false);
        List<Long> queryDeptIds = commonService.getAllChildrenDeptIdsByDeptIds(null, q.getDeptIds());
        // START 数据权限相关，业务公司按发起组取，会计工厂按承接组取
        if (userDept.getDeptType() == 1) {
            if (!ObjectUtils.isEmpty(queryDeptIds)) {
                wrapper.in(ValueAddedDeliveryOrder::getInitiateDeptId, queryDeptIds);
            }
            if (!userDept.getIsAdmin()) {
                wrapper.in(ValueAddedDeliveryOrder::getInitiateDeptId, userDept.getDeptIds());
            }
        } else {
            if (!ObjectUtils.isEmpty(queryDeptIds)) {
                wrapper.and(queryWrapper -> queryWrapper
                        .in(ValueAddedDeliveryOrder::getAccountingDeptId, queryDeptIds)
                        .or()
                        .in(ValueAddedDeliveryOrder::getAccountingTopDeptId, queryDeptIds));
            }
            if (!userDept.getIsAdmin()) {
                wrapper.and(queryWrapper -> queryWrapper
                        .in(ValueAddedDeliveryOrder::getAccountingDeptId, userDept.getDeptIds())
                        .or()
                        .in(ValueAddedDeliveryOrder::getAccountingTopDeptId, userDept.getDeptIds()));
            }
        }
        // END 数据权限相关
        // 处理 itemTypeCode 查询条件
        if (StringUtils.isNotEmpty(q.getItemTypeCode())) {
            List<Integer> itemIds = valueAddedItemTypeService.getItemIdsByItemTypeCode(q.getItemTypeCode());
            if (itemIds.isEmpty()) {
                wrapper.eq(ValueAddedDeliveryOrder::getId, -1); // 使用不存在的ID确保返回空结果
                return wrapper;
            } else {
                wrapper.in(ValueAddedDeliveryOrder::getValueAddedItemTypeId, itemIds);
            }
        }


        // 基础文本/数字条件
        wrapper
                .eq(StringUtils.isNotEmpty(q.getDeliveryOrderNo()), ValueAddedDeliveryOrder::getDeliveryOrderNo, q.getDeliveryOrderNo())
                .eq(StringUtils.isNotEmpty(q.getCreditCode()), ValueAddedDeliveryOrder::getCreditCode, q.getCreditCode())
                .eq(StringUtils.isNotEmpty(q.getTaxNo()), ValueAddedDeliveryOrder::getTaxNo, q.getTaxNo())
                .eq(q.getTaxpayerType() != null, ValueAddedDeliveryOrder::getTaxpayerType, q.getTaxpayerType())
                .eq(q.getValueAddedItemTypeId() != null, ValueAddedDeliveryOrder::getValueAddedItemTypeId, q.getValueAddedItemTypeId())
                .eq(q.getInitiateDeptId() != null, ValueAddedDeliveryOrder::getInitiateDeptId, q.getInitiateDeptId())
                .eq(q.getBusinessDeptId() != null, ValueAddedDeliveryOrder::getBusinessDeptId, q.getBusinessDeptId())
                .eq(q.getBusinessTopDeptId() != null, ValueAddedDeliveryOrder::getBusinessTopDeptId, q.getBusinessTopDeptId())
                .eq(q.getAccountingDeptId() != null, ValueAddedDeliveryOrder::getAccountingDeptId, q.getAccountingDeptId())
                .eq(q.getCreateUid() != null, ValueAddedDeliveryOrder::getCreateUid, q.getCreateUid());
        if (!StringUtils.isEmpty(q.getStatus())) {
            // 状态筛选支持多选
            wrapper.in(ValueAddedDeliveryOrder::getStatus, Arrays.asList(q.getStatus().split(",")));
        }

        // 账期范围查询：查询条件的账期范围与数据库记录的账期范围有交集
        if (q.getAccountingPeriodStart() != null && q.getAccountingPeriodEnd() != null) {
            // 查询账期范围与记录账期范围有交集的记录
            // 条件：查询开始 <= 记录结束 AND 查询结束 >= 记录开始
            Integer queryStart = q.getAccountingPeriodStart();
            Integer queryEnd = q.getAccountingPeriodEnd();
            if (queryStart > queryEnd) {
                // 容错：如果开始>结束，自动交换
                Integer temp = queryStart;
                queryStart = queryEnd;
                queryEnd = temp;
            }
            wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, queryEnd)
                    .ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, queryStart);
        } else if (q.getAccountingPeriodStart() != null) {
            // 只有开始时间：记录的结束时间 >= 查询开始时间
            wrapper.ge(ValueAddedDeliveryOrder::getAccountingPeriodEnd, q.getAccountingPeriodStart());
        } else if (q.getAccountingPeriodEnd() != null) {
            // 只有结束时间：记录的开始时间 <= 查询结束时间
            wrapper.le(ValueAddedDeliveryOrder::getAccountingPeriodStart, q.getAccountingPeriodEnd());
        }

        // DDL 日期范围
        LocalDate ddlStart = q.getDdlStart();
        LocalDate ddlEnd = q.getDdlEnd();
        if (ddlStart != null && ddlEnd != null) {
            if (!ddlEnd.isBefore(ddlStart)) {
                wrapper.between(ValueAddedDeliveryOrder::getDdl, ddlStart, ddlEnd);
            } else {
                wrapper.between(ValueAddedDeliveryOrder::getDdl, ddlEnd, ddlStart);
            }
        } else if (ddlStart != null) {
            wrapper.ge(ValueAddedDeliveryOrder::getDdl, ddlStart);
        } else if (ddlEnd != null) {
            wrapper.le(ValueAddedDeliveryOrder::getDdl, ddlEnd);
        }

        // 是否已承接条件
        if (q.getHasAccountDeptId() != null) {
            if (q.getHasAccountDeptId()) {
                wrapper.isNotNull(ValueAddedDeliveryOrder::getAccountingDeptId);
            } else {
                wrapper.isNull(ValueAddedDeliveryOrder::getAccountingDeptId);
            }
        }

        // 发起时间范围
        if (!StringUtils.isEmpty(q.getInitiateTimeStart()) && !StringUtils.isEmpty(q.getInitiateTimeEnd())) {
            wrapper.between(ValueAddedDeliveryOrder::getInitiateTime, q.getInitiateTimeStart(), q.getInitiateTimeEnd());
        }

        // 关键词搜索（模糊查询客户名称和统一社会信用代码）
        if (StringUtils.isNotEmpty(q.getKeyWord())) {
            wrapper.and(w -> w.like(ValueAddedDeliveryOrder::getCustomerName, q.getKeyWord())
                    .or().like(ValueAddedDeliveryOrder::getCreditCode, q.getKeyWord()));
        }

        // 排序：默认创建时间倒序
        wrapper.orderByDesc(ValueAddedDeliveryOrder::getCreateTime);

        return wrapper;
    }


    @Override
    public IPage<DeliveryOrderVO> queryVOPage(Long deptId, DeliveryOrderQuery q) {
        try {
            // 创建分页对象
            IPage<DeliveryOrderVO> page = new Page<>(
                    q.getPageNum() != null ? q.getPageNum() : 1,
                    q.getPageSize() != null ? q.getPageSize() : 10
            );
            // 数据权限相关，业务公司按发起组取，会计工厂按承接组取
            UserDeptDTO userDept = remoteDeptService.userDeptList(q.getUserId(), q.getDeptId()).getDataThrowException();
            if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
                return page.setRecords(new ArrayList<>());
            }
            LambdaQueryWrapper<ValueAddedDeliveryOrder> wrapper = buildQueryWrapper(q, userDept);
            IPage<ValueAddedDeliveryOrder> entityPage = new Page<>(page.getCurrent(), page.getSize());
            entityPage = this.page(entityPage, wrapper);

            if (entityPage.getRecords().isEmpty()) {
                return page.setRecords(new ArrayList<>());
            }

            Set<Integer> itemTypeIds = entityPage.getRecords().stream()
                    .map(ValueAddedDeliveryOrder::getValueAddedItemTypeId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询增值事项类型信息，建立itemTypeId到itemName和完整VO对象的映射
            Map<Integer, String> itemTypeIdToNameMap = new HashMap<>();
            Map<Integer, ValueAddedItemTypeVO> itemTypeIdToVOMap = new HashMap<>();
            if (!itemTypeIds.isEmpty()) {
                try {
                    // 批量查询所有需要的增值事项类型
                    List<ValueAddedItemType> itemTypes = valueAddedItemTypeService.listByIds(itemTypeIds);

                    // 构建映射关系
                    for (ValueAddedItemType itemType : itemTypes) {
                        if (itemType != null && StringUtils.isNotEmpty(itemType.getItemName()) && !Boolean.TRUE.equals(itemType.getIsDel())) {
                            itemTypeIdToNameMap.put(itemType.getId().intValue(), itemType.getItemName());
                            itemTypeIdToVOMap.put(itemType.getId().intValue(), convertToItemTypeVO(itemType));
                        }
                    }

                } catch (Exception e) {
                    log.error("Failed to load item type mappings from batch query", e);
                }
            }

            // 转换实体对象到VO对象
            final Map<Integer, String> finalItemTypeIdToNameMap = itemTypeIdToNameMap;
            final Map<Integer, ValueAddedItemTypeVO> finalItemTypeIdToVOMap = itemTypeIdToVOMap;
            List<DeliveryOrderVO> voList = entityPage.getRecords().stream()
                    .map(order -> convertToDeliveryOrderVO(order, finalItemTypeIdToNameMap, finalItemTypeIdToVOMap))
                    .collect(Collectors.toList());

            // 批量获取会计信息名称
            fillAccountingNameInfo(voList);
            // 批量设置发起人名称
            fillInitiateDeptNameInfo(voList);

            // 批量获取交付文件数量
            fillDeliveryFileCnt(voList);

            return page.setRecords(voList).setTotal(entityPage.getTotal());
        } catch (Exception e) {
            log.error("Failed to query delivery orders with pagination", e);
            throw new RuntimeException("分页查询增值交付单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<CommonFileVO> buildValueAddedDeliveryOrderFiles(List<DeliveryOrderVO> deliveryOrderList, String exportTypes) {
        if (StringUtils.isEmpty(exportTypes)) {
            return Collections.emptyList();
        }
        List<Integer> downloadFileTypes = Arrays.stream(exportTypes.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        for (Integer downloadFileType : downloadFileTypes) {
            ValueAddedFileType fileType = ValueAddedFileType.getByCode(downloadFileType);
            if (null == fileType) {
                continue;
            }
            String baseDir = fileType.getName();
            for (DeliveryOrderVO resultDTO : deliveryOrderList) {
                String dirPath = baseDir + "/" + resultDTO.getCustomerName() + "-" + resultDTO.getCreditCode() + "-" + (StringUtils.isEmpty(resultDTO.getAccountingPeriodRange()) ? "未设置账期" : resultDTO.getAccountingPeriodRange()) + "/" + resultDTO.getItemName();
                List<CommonFileVO> fileList;
                if (downloadFileType.equals(ValueAddedFileType.DELIVERY_MATERIAL.getCode())) {
                    fileList = ObjectUtils.isEmpty(resultDTO.getDeliveryFiles()) ? Collections.emptyList() : resultDTO.getDeliveryFiles().stream().map(file -> CommonFileVO.builder()
                            .fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList());
                } else if (downloadFileType.equals(ValueAddedFileType.DELIVERY_ATTACHMENT.getCode())) {
                    fileList = ObjectUtils.isEmpty(resultDTO.getDeliveryAttachments()) ? Collections.emptyList() : resultDTO.getDeliveryAttachments().stream().map(file -> CommonFileVO.builder()
                            .fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList());
                } else if (downloadFileType.equals(ValueAddedFileType.STANDARD_ATTACHMENT.getCode())) {
                    fileList = ObjectUtils.isEmpty(resultDTO.getStandardAttachments()) ? Collections.emptyList() : resultDTO.getStandardAttachments().stream().map(file -> CommonFileVO.builder()
                            .fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList());
                } else {
                    fileList = Collections.emptyList();
                }
                if (!ObjectUtils.isEmpty(fileList)) {
                    for (CommonFileVO file : fileList) {
                        file.setBaseDir(dirPath);
                    }
                    files.addAll(fileList);
                }
            }
        }
        if (!ObjectUtils.isEmpty(files)) {
            StringUtils.dealFileNames(files);
        }
        return files;
    }

    /**
     * 批量填充会计信息名称
     *
     * @param voList VO列表
     */
    private void fillAccountingNameInfo(List<DeliveryOrderVO> voList) {
        try {
            // 收集所有非空的accountingDeptId
            List<Long> accountingDeptIds = voList.stream().map(DeliveryOrderVO::getAccountingDeptId).filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            if (accountingDeptIds.isEmpty()) {
                return;
            }
            // 批量获取会计信息名称
            Map<Long, String> accountingNameMap = commonService.getBatchDeptEmployeeByDeptIds(accountingDeptIds);
            // 填充到VO中
            voList.forEach(vo -> {
                if (vo.getAccountingDeptId() != null) {
                    String accountingName = accountingNameMap.get(vo.getAccountingDeptId());
                    vo.setAccountingNameInfo(accountingName);
                }
            });

        } catch (Exception e) {
            log.warn("Failed to fill accounting name info: {}", e.getMessage());
        }
    }

    /**
     * 批量填充发起人信息名称
     *
     * @param voList VO列表
     */
    private void fillInitiateDeptNameInfo(List<DeliveryOrderVO> voList) {
        try {
            // 收集所有非空的initiateDeptId
            List<Long> initiateDeptIds = voList.stream().map(DeliveryOrderVO::getInitiateDeptId).filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            if (initiateDeptIds.isEmpty()) {
                return;
            }
            // 批量获取会计信息名称
            Map<Long, String> initiateDeptNameMap = commonService.getBatchDeptEmployeeByDeptIds(initiateDeptIds);
            // 填充到VO中
            voList.forEach(vo -> {
                if (vo.getInitiateDeptId() != null) {
                    String initiateDeptName = initiateDeptNameMap.get(vo.getInitiateDeptId());
                    vo.setInitiateDeptInfo(initiateDeptName);
                }
            });

        } catch (Exception e) {
            log.warn("Failed to fill accounting name info: {}", e.getMessage());
        }
    }

    /**
     * 批量填充交付文件数量
     *
     * @param voList VO列表
     */
    private void fillDeliveryFileCnt(List<DeliveryOrderVO> voList) {
        try {
            // 收集所有交付单编号
            List<String> deliveryOrderNos = voList.stream()
                    .map(DeliveryOrderVO::getDeliveryOrderNo).filter(StringUtils::isNotEmpty)
                    .distinct().collect(Collectors.toList());
            if (deliveryOrderNos.isEmpty()) {
                return;
            }
            LambdaQueryWrapper<ValueAddedFile> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(ValueAddedFile::getDeliveryOrderNo, deliveryOrderNos)
                    .in(ValueAddedFile::getFileType, ValueAddedFileType.DELIVERY_ATTACHMENT.getCode(), ValueAddedFileType.DELIVERY_MATERIAL.getCode(), ValueAddedFileType.STANDARD_ATTACHMENT.getCode())
                    .eq(ValueAddedFile::getIsDel, false);

            List<ValueAddedFile> files = valueAddedFileService.list(wrapper);

            // 按交付单编号，文件类型分组
            Map<String, Map<Integer, List<ValueAddedFile>>> fileMap = files.stream().collect(Collectors.groupingBy(
                    ValueAddedFile::getDeliveryOrderNo,
                    Collectors.groupingBy(
                            ValueAddedFile::getFileType
                    )
            ));

            // 按交付单编号分组统计数量
            Map<String, Long> fileCntMap = files.stream().filter(f -> ValueAddedFileType.DELIVERY_ATTACHMENT.getCode().equals(f.getFileType()))
                    .collect(Collectors.groupingBy(
                            ValueAddedFile::getDeliveryOrderNo,
                            Collectors.counting()
                    ));

            // 填充到VO中
            voList.forEach(vo -> {
                Long count = fileCntMap.get(vo.getDeliveryOrderNo());
                Map<Integer, List<ValueAddedFile>> fileTypeMap = fileMap.getOrDefault(vo.getDeliveryOrderNo(), new HashMap<>());
                vo.setDeliveryFileCnt(count != null ? count.intValue() : 0);
                // 以下导出需要用到
                vo.setDeliveryAttachments(fileTypeMap.containsKey(ValueAddedFileType.DELIVERY_ATTACHMENT.getCode()) ? convertToFileDTO(fileTypeMap.get(ValueAddedFileType.DELIVERY_ATTACHMENT.getCode()), null) : Collections.emptyList());
                vo.setDeliveryFiles(fileTypeMap.containsKey(ValueAddedFileType.DELIVERY_MATERIAL.getCode()) ? convertToFileDTO(fileTypeMap.get(ValueAddedFileType.DELIVERY_MATERIAL.getCode()), null) : Collections.emptyList());
                vo.setStandardAttachments(fileTypeMap.containsKey(ValueAddedFileType.STANDARD_ATTACHMENT.getCode()) ? convertToFileDTO(fileTypeMap.get(ValueAddedFileType.STANDARD_ATTACHMENT.getCode()), null) : Collections.emptyList());
            });

        } catch (Exception e) {
            log.warn("Failed to fill delivery file count: {}", e.getMessage());
            voList.forEach(vo -> vo.setDeliveryFileCnt(0));
        }
    }


    /**
     * 将实体对象转换为DeliveryOrderVO对象
     *
     * @param order               增值交付单实体对象
     * @param itemTypeIdToNameMap itemTypeId到itemName的映射
     * @param itemTypeIdToVOMap   itemTypeId到完整ValueAddedItemTypeVO的映射
     * @return DeliveryOrderVO对象
     */
    private DeliveryOrderVO convertToDeliveryOrderVO(ValueAddedDeliveryOrder order,
                                                     Map<Integer, String> itemTypeIdToNameMap,
                                                     Map<Integer, ValueAddedItemTypeVO> itemTypeIdToVOMap) {
        DeliveryOrderVO vo = new DeliveryOrderVO();

        // 使用BeanUtils复制基础字段
        BeanUtils.copyProperties(order, vo);

        StringBuilder accountingPeriodRange = new StringBuilder();
        if (!Objects.isNull(vo.getAccountingPeriodStart())) {
            accountingPeriodRange.append(vo.getAccountingPeriodStart());
        }
        if (!Objects.isNull(vo.getAccountingPeriodEnd())) {
            if (StringUtils.isEmpty(accountingPeriodRange)) {
                accountingPeriodRange.append(vo.getAccountingPeriodEnd());
            } else {
                accountingPeriodRange.append("-").append(vo.getAccountingPeriodEnd());
            }
        }
        vo.setAccountingPeriodRange(accountingPeriodRange.toString());
        vo.setTotalWithholdingAmountStr(Objects.isNull(vo.getTotalWithholdingAmount()) ? "" : vo.getTotalWithholdingAmount().stripTrailingZeros().toPlainString());
        // 映射itemId到itemName和完整的valueAddedItemType对象
        if (order.getValueAddedItemTypeId() != null) {
            String itemName = itemTypeIdToNameMap.get(order.getValueAddedItemTypeId());
            vo.setItemName(itemName != null ? itemName : "");

            ValueAddedItemTypeVO itemTypeVO = itemTypeIdToVOMap.get(order.getValueAddedItemTypeId());
            vo.setValueAddedItemType(itemTypeVO);
        }

        // 映射status到statusDesc
        if (StringUtils.isNotEmpty(order.getStatus())) {
            ValueAddedDeliveryOrderStatus status = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            if (status != null) {
                vo.setStatusName(status.getDescription());
            }
        }

        if (order.getTaxpayerType() != null) {
            String taxpayerTypeName = TaxpayerTypeEnum.getDescriptionByCode(order.getTaxpayerType());
            vo.setTaxpayerTypeName(taxpayerTypeName);
        }

        // 映射accountingMethod到accountingMethodName
        if (order.getAccountingMethod() != null) {
            String accountingMethodName = AccountingMethodEnum.getDescriptionByCode(order.getAccountingMethod());
            vo.setAccountingMethodName(accountingMethodName);
        }

        return vo;
    }

    /**
     * 验证订单VO
     */
    private void validateOrderVO(DeliveryOrderUpsertReq orderVO) {
        // 验证交付单编号（必填字段）
        if (StringUtils.isEmpty(orderVO.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 验证状态（如果提供了状态）
        if (StringUtils.isNotEmpty(orderVO.getStatus()) && !ValueAddedDeliveryOrderStatus.isValid(orderVO.getStatus())) {
            throw new IllegalArgumentException("无效的交付状态: " + orderVO.getStatus());
        }

        // 验证纳税性质
        if (orderVO.getTaxpayerType() != null && !TaxpayerTypeEnum.isValid(orderVO.getTaxpayerType())) {
            throw new IllegalArgumentException("无效的纳税性质: " + orderVO.getTaxpayerType());
        }

        // 验证入账方式
        if (orderVO.getAccountingMethod() != null && !AccountingMethodEnum.isValid(orderVO.getAccountingMethod())) {
            throw new IllegalArgumentException("无效的入账方式: " + orderVO.getAccountingMethod());
        }

        // 验证账期逻辑（账期格式为YYYYMM的整数，如：202301）
        if (orderVO.getAccountingPeriodStart() != null && orderVO.getAccountingPeriodEnd() != null) {
            if (orderVO.getAccountingPeriodStart() > orderVO.getAccountingPeriodEnd()) {
                throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
            }
        }
    }

    /**
     * 查找现有记录
     */
    private ValueAddedDeliveryOrder findExistingOrder(ValueAddedDeliveryOrder order) {
        if (StringUtils.isNotEmpty(order.getDeliveryOrderNo())) {
            return getByDeliveryOrderNo(order.getDeliveryOrderNo());
        }
        return null;
    }

    /**
     * 更新现有记录
     */
    private void updateExistingOrder(ValueAddedDeliveryOrder existingOrder, ValueAddedDeliveryOrder newOrder) {
        // 保留原有的ID、交付单编号和创建相关信息
        Long originalId = existingOrder.getId();
        String originalDeliveryOrderNo = existingOrder.getDeliveryOrderNo();
        String originalCreateBy = existingOrder.getCreateBy();
        Long originalCreateUid = existingOrder.getCreateUid();
        BeanUtils.copyIgnoreNullAndEmpty(newOrder, existingOrder);
        // 恢复不应该被更新的字段
        existingOrder.setId(originalId);
        existingOrder.setDeliveryOrderNo(originalDeliveryOrderNo);
        existingOrder.setCreateBy(originalCreateBy);
        existingOrder.setCreateUid(originalCreateUid);
    }

    /**
     * 生成交付单标题
     * 格式：增值交付单-{itemName}-{accountingPeriodStart}-{accountingPeriodEnd}
     *
     * @param order 交付单对象
     * @return 生成的标题
     */
    private String generateTitle(ValueAddedDeliveryOrder order) {
        try {
            // 获取增值事项名称
            String itemName = "未知事项";
            if (order.getValueAddedItemTypeId() != null) {
                ValueAddedItemType itemType = valueAddedItemTypeService.getById(order.getValueAddedItemTypeId());
                if (itemType != null && StringUtils.isNotEmpty(itemType.getItemName())) {
                    itemName = itemType.getItemName();
                }
            }
            // 格式化账期
            String periodStart = order.getAccountingPeriodStart() != null ?
                    String.valueOf(order.getAccountingPeriodStart()) : "未设置";
            String periodEnd = order.getAccountingPeriodEnd() != null ?
                    String.valueOf(order.getAccountingPeriodEnd()) : "未设置";

            // 生成标题
            String title = String.format("%s-%s-%s", itemName, periodStart, periodEnd);

            return title;
        } catch (Exception e) {
            log.warn("Failed to generate title for delivery order, using default title. Error: {}", e.getMessage());
            return "未知事项-未设置-未设置";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(StatusChangeRequestDTO request) {
        try {
            log.info("Starting transactional status change for order: {}", request.getDeliveryOrderNo());
            // 1. 获取当前交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(request.getDeliveryOrderNo());
            if (order == null) {
                throw new IllegalArgumentException("交付单不存在: " + request.getDeliveryOrderNo());
            }
            // 2. 解析当前状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            if (currentStatus == null) {
                throw new IllegalArgumentException("当前状态无效: " + order.getStatus());
            }
            // 3. 智能推断目标状态或使用指定的目标状态
            ValueAddedDeliveryOrderStatus targetStatus = determineTargetStatus(request, currentStatus);
            // 4. 状态机管理器处理验证
            valueAddedDeliveryOrderStateMachineManager.validateAndChangeStatus(order, currentStatus, targetStatus, request);
            // 如果操作类型是驳回，进行特殊处理
            if (ValueAddedOperTypeEnum.REJECT.getCode().equals(request.getOperTypeName())) {
                // 将目标状态追加到交付要求后面
                if (request.getRemark() != null && !request.getRemark().trim().isEmpty()) {
                    String current = order.getRequirements();
                    order.setRequirements(current.trim().isEmpty() ? request.getRemark() : current + " " + request.getRemark());
                }
            }

            // 如果目标状态是已提交待交付，设置发起时间
            if (ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY.getCode().equals(targetStatus.getCode())) {
                order.setInitiateTime(new Date());
            }

            // 5. ValueAddedFile表
            saveAttachmentFiles(request);

            // 6. 执行数据库更新（在事务中）
            boolean updateResult = updateById(order);
            if (!updateResult) {
                log.error("Database update failed for order: {}, transaction will be rolled back", request.getDeliveryOrderNo());
                throw new RuntimeException("状态更新失败，事务已回滚");
            }

            // 8. 记录状态变更操作日志
            saveStatusChangeBusinessLog(order.getId(), currentStatus, targetStatus, request);
        } catch (Exception e) {
            log.error("Transactional status change failed for order: {}, transaction rolled back, error: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 确定目标状态：使用指定状态
     *
     * @param request       状态变更请求
     * @param currentStatus 当前状态
     * @return 目标状态
     */
    private ValueAddedDeliveryOrderStatus determineTargetStatus(StatusChangeRequestDTO request, ValueAddedDeliveryOrderStatus currentStatus) {
        // 如果提供了targetStatus，直接使用
        if (StringUtils.isNotEmpty(request.getTargetStatus())) {
            ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
            if (targetStatus == null) {
                throw new IllegalArgumentException("目标状态无效: " + request.getTargetStatus());
            }
            return targetStatus;
        }
        throw new IllegalArgumentException(
                String.format("当前状态 %s 有多个可能的转换路径，必须明确指定 targetStatus。", currentStatus.getDescription()));
    }


    @Override
    public List<ValueAddedDeliveryOrderStatus> getAvailableStatuses(String deliveryOrderNo) {
        try {
            log.info("Getting available statuses for order: {}", deliveryOrderNo);

            // 参数验证
            if (StringUtils.isEmpty(deliveryOrderNo)) {
                throw new IllegalArgumentException("交付单编号不能为空");
            }

            // 获取当前交付单信息
            ValueAddedDeliveryOrder order = getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                log.warn("Delivery order not found: {}", deliveryOrderNo);
                return new ArrayList<>();
            }

            // 解析当前状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            if (currentStatus == null) {
                log.warn("Invalid current status for order: {}, status: {}", deliveryOrderNo, order.getStatus());
                return new ArrayList<>();
            }

            // 获取可用的下一状态
            List<ValueAddedDeliveryOrderStatus> availableStatuses =
                    valueAddedDeliveryOrderStateMachineManager.getAvailableNextStatuses(currentStatus);

            log.info("Found {} available statuses for order: {}", availableStatuses.size(), deliveryOrderNo);
            return availableStatuses;

        } catch (Exception e) {
            log.error("Failed to get available statuses for order: {}, error: {}", deliveryOrderNo, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStatus(SaveStatusReqVO request) {
        try {
            log.info("Starting save status operation for order: {}, target status: {}",
                    request.getDeliveryOrderNo(), request.getTargetStatus());

            // 查找现有记录
            ValueAddedDeliveryOrder existingOrder = getByDeliveryOrderNo(request.getDeliveryOrderNo());
            if (existingOrder == null) {
                throw new IllegalArgumentException("未找到对应的增值交付单: " + request.getDeliveryOrderNo());
            }

            // 验证当前状态是否适用于此操作（待交付、待扣款状态）
            String currentStatus = existingOrder.getStatus();
            if (!isValidStatusForSave(currentStatus)) {
                log.warn("Current status {} is not valid for save status operation", currentStatus);
                throw new IllegalArgumentException("当前状态不支持此操作，仅支持待交付、待扣款状态");
            }

            // 更新相关字段

            // 如果提供了总扣缴额，则更新
            if (request.getTotalWithholdingAmount() != null) {
                existingOrder.setTotalWithholdingAmount(request.getTotalWithholdingAmount());
            }


            // 执行数据库更新
            boolean updateResult = updateById(existingOrder);
            if (!updateResult) {
                log.error("Database update failed for order: {}, transaction will be rolled back", request.getDeliveryOrderNo());
                throw new RuntimeException("状态保存失败，事务已回滚");
            }

        } catch (Exception e) {
            log.error("Save status failed for order: {}, error: {}",
                    request != null ? request.getDeliveryOrderNo() : "unknown", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DispatchResultDTO batchDispatch(@NotNull List<String> deliveryOrders, @NotNull Long accountingDeptId, DispatchRequestDTO request) {
        List<DispatchErrorDTO> errors = new ArrayList<>();
        List<String> successOrderNos = new ArrayList<>();
        String batchNo = null;

        try {

            // 过滤空值并去重
            List<String> validOrderNos = deliveryOrders.stream().filter(StringUtils::isNotEmpty)
                    .map(String::trim).distinct().collect(Collectors.toList());

            if (validOrderNos.isEmpty()) {
                throw new IllegalArgumentException("没有有效的交付单编号");
            }

            // 查询所有交付单详细信息
            List<ValueAddedDeliveryOrder> existingOrders = listByDeliveryOrderNos(validOrderNos);
            Map<String, ValueAddedDeliveryOrder> orderMap = existingOrders.stream()
                    .collect(Collectors.toMap(ValueAddedDeliveryOrder::getDeliveryOrderNo, Function.identity()));

            // 分离存在和不存在的交付单
            List<String> existingOrderNos = new ArrayList<>();
            String targetDeptName = commonService.getDeptEmployeeByDeptId(accountingDeptId);
            Map<String, Object> operContentMap = new LinkedHashMap<>();
            operContentMap.put("分派给", targetDeptName);
            for (String orderNo : validOrderNos) {
                ValueAddedDeliveryOrder order = orderMap.get(orderNo);
                if (order == null) {
                    // 交付单不存在，记录异常
                    errors.add(DispatchErrorDTO.createError(orderNo, "交付单不存在"));
                } else {
                    existingOrderNos.add(orderNo);
                }
            }

            // 批量更新存在的交付单
            if (!existingOrderNos.isEmpty()) {
                LambdaUpdateWrapper<ValueAddedDeliveryOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(ValueAddedDeliveryOrder::getDeliveryOrderNo, existingOrderNos)
                        .eq(ValueAddedDeliveryOrder::getIsDel, false)
                        .set(ValueAddedDeliveryOrder::getAccountingDeptId, accountingDeptId)
                        .set(ValueAddedDeliveryOrder::getAccountingCreateTime, new Date());

                int updateCount = baseMapper.update(null, updateWrapper);
                successOrderNos.addAll(existingOrderNos);

                String operContent = JSONObject.toJSONString(operContentMap);
                existingOrderNos.forEach(orderNo -> saveDispatchBusinessLog(orderMap.get(orderNo).getId(), request, operContent));
                log.info("Batch update completed: expected={}, actual={}", existingOrderNos.size(), updateCount);
            }

            // 缓存异常数据（只缓存交付单不存在的异常）
            if (!errors.isEmpty()) {
                batchNo = IdUtils.fastSimpleUUID();
                cacheDispatchErrorData(batchNo, errors);
            }

            // 构建返回结果
            DispatchResultDTO result = DispatchResultDTO.builder().total(validOrderNos.size())
                    .succCnt(successOrderNos.size()).failCnt(errors.size()).batchNo(batchNo).build();

            return result;
        } catch (Exception e) {
            log.error("Batch dispatch failed for accounting dept: {}, error: {}", accountingDeptId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 缓存分派异常数据
     */
    private void cacheDispatchErrorData(String batchNo, List<DispatchErrorDTO> errors) {
        try {
            redisService.setLargeCacheList(
                    CacheConstants.BATCH_OPERATION_ERROR_RECORD + batchNo,
                    errors, batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(), TimeUnit.SECONDS
            );
            log.info("Cached {} dispatch error records with batchNo: {}", errors.size(), batchNo);
        } catch (Exception e) {
            log.error("Failed to cache dispatch error data for batchNo: {}", batchNo, e);
        }
    }

    /**
     * 验证当前状态是否适用于保存状态操作
     * 仅支持待交付、待扣款状态
     *
     * @param currentStatus 当前状态
     * @return 是否有效
     */
    private boolean isValidStatusForSave(String currentStatus) {
        if (StringUtils.isEmpty(currentStatus)) {
            return false;
        }

        ValueAddedDeliveryOrderStatus status = ValueAddedDeliveryOrderStatus.getByCode(currentStatus);
        if (status == null) {
            return false;
        }

        // 仅支持待交付、待扣款状态
        return status == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY ||
                status == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION;
    }

    /**
     * 构建基础DeliveryOrderVO对象
     *
     * @param order 交付单实体
     * @return DeliveryOrderVO对象
     */
    private DeliveryOrderVO buildBaseDeliveryOrderVO(ValueAddedDeliveryOrder order) {
        DeliveryOrderVO vo = new DeliveryOrderVO();
        BeanUtils.copyProperties(order, vo);
        vo.setInitiateDeptPath(calculateInitiateDeptPath(order.getInitiateDeptId()));
        if (order.getValueAddedItemTypeId() != null) {
            try {
                ValueAddedItemType itemType = valueAddedItemTypeService.getById(order.getValueAddedItemTypeId());
                if (itemType != null && StringUtils.isNotEmpty(itemType.getItemName()) && !Boolean.TRUE.equals(itemType.getIsDel())) {
                    vo.setItemName(itemType.getItemName());
                    // 设置完整的增值事项类型对象
                    ValueAddedItemTypeVO itemTypeVO = convertToItemTypeVO(itemType);
                    vo.setValueAddedItemType(itemTypeVO);
                }
            } catch (Exception e) {
                log.warn("Failed to get item type info for id: {}", order.getValueAddedItemTypeId(), e);
            }
        }
        // 映射taxpayerType到taxpayerTypeName
        if (order.getTaxpayerType() != null) {
            String taxpayerTypeName = TaxType.getByCode(order.getTaxpayerType()).getDesc();
            vo.setTaxpayerTypeName(taxpayerTypeName);
            log.debug("Mapped taxpayerType {} to taxpayerTypeName: {}", order.getTaxpayerType(), taxpayerTypeName);
        }

        // 映射accountingMethod到accountingMethodName
        if (order.getAccountingMethod() != null) {
            String accountingMethodName = AccountingMethodEnum.getDescriptionByCode(order.getAccountingMethod());
            vo.setAccountingMethodName(accountingMethodName);
        }

        if (!Objects.isNull(order.getCustomerId())) {
            CCustomerService customerService = cuCustomerServiceService.getById(order.getCustomerId());
            if (!Objects.isNull(customerService) && !customerService.getIsDel()) {
                if (!Objects.isNull(customerService.getAdvisorDeptId())) {
                    vo.setCustomerServiceAdvisorInfo(commonService.getDeptEmployeeByDeptId(customerService.getAdvisorDeptId()));
                }
                if (!Objects.isNull(customerService.getAccountingDeptId())) {
                    vo.setCustomerServiceAccountingInfo(commonService.getDeptEmployeeByDeptId(customerService.getAccountingDeptId()));
                }
            }
        }
        return vo;
    }

    /**
     * 设置扩展数据
     *
     * @param vo                   交付单VO
     * @param deliveryOrderNo      交付单编号
     * @param valueAddedItemTypeId 增值事项类型ID
     */
    private void setExtendedData(DeliveryOrderVO vo, String deliveryOrderNo, Integer valueAddedItemTypeId) {
        try {
            // 获取国税账号对象（bizType=3）
            vo.setNationalTaxAccount(getNationalTaxAccount(deliveryOrderNo));

            // 获取个税账号对象（bizType=4）
            vo.setPersonalTaxAccount(getPersonalTaxAccount(deliveryOrderNo));

            // 根据itemCode获取员工信息列表
            vo.setEmployeeInfoList(getEmployeeInfoList(deliveryOrderNo, valueAddedItemTypeId));

            // 实际员工数量
            vo.setTotalEmployeeCount(getTotalEmployeeCount(deliveryOrderNo, valueAddedItemTypeId));

            // 获取所有文件列表（一次查询，按类型分组）
            setFilesByType(vo, deliveryOrderNo);

            // 获取库存信息列表
            setStockList(vo, deliveryOrderNo);
        } catch (Exception e) {
            log.error("Failed to set extended data for delivery order: {}", deliveryOrderNo, e);
            // 设置默认值，避免返回null
            vo.setEmployeeInfoList(new ArrayList<>());
            vo.setDeliveryFiles(new ArrayList<>());
            vo.setStockList(new ArrayList<>());
        }
    }

    /**
     * 获取国税账号对象（bizType=3）
     *
     * @param deliveryOrderNo 交付单编号
     * @return 国税账号VO对象，如果不存在则返回null
     */
    private NationalTaxAccountVO getNationalTaxAccount(String deliveryOrderNo) {
        try {
            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, ValueAddedConstants.BizType.NATIONAL_TAX_ACCOUNT)
                    .last("LIMIT 1");

            ValueAddedEmployee employee = valueAddedEmployeeService.getOne(wrapper);
            return employee != null ? convertToNationalTaxAccountVO(employee) : null;
        } catch (Exception e) {
            log.error("Failed to get national tax account for order: {}", deliveryOrderNo, e);
            return null;
        }
    }

    /**
     * 获取个税账号对象（bizType=4）
     *
     * @param deliveryOrderNo 交付单编号
     * @return 个税账号VO对象，如果不存在则返回null
     */
    private PersonalTaxAccountVO getPersonalTaxAccount(String deliveryOrderNo) {
        try {
            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, ValueAddedConstants.BizType.PERSONAL_TAX_ACCOUNT)
                    .last("LIMIT 1");

            ValueAddedEmployee employee = valueAddedEmployeeService.getOne(wrapper);
            return employee != null ? convertToPersonalTaxAccountVO(employee) : null;
        } catch (Exception e) {
            log.error("Failed to get personal tax account for order: {}", deliveryOrderNo, e);
            return null;
        }
    }

    /**
     * 根据itemCode获取员工信息列表
     * 当itemCode为TAX_SOCIAL_INSURANCE时bizType对应1，TAX_PERSONAL_SALARY/TAX_PERSONAL_OTHER对应bizType为2，最多查50条
     *
     * @param deliveryOrderNo      交付单编号
     * @param valueAddedItemTypeId 增值事项类型ID
     * @return 员工信息列表
     */
    private List<EmployeeInfo> getEmployeeInfoList(String deliveryOrderNo, Integer valueAddedItemTypeId) {
        try {
            // 通过增值事项类型ID获取itemCode
            String itemCode = getItemCodeByTypeId(valueAddedItemTypeId);
            if (StringUtils.isEmpty(itemCode)) {
                log.warn("Cannot find item code for valueAddedItemTypeId: {}", valueAddedItemTypeId);
                return new ArrayList<>();
            }

            // 根据itemCode确定bizType
            Integer bizType = determineBizTypeByItemCode(itemCode);
            if (bizType == null) {
                return new ArrayList<>();
            }

            log.info("Getting employee info list for order: {}, itemCode: {}, bizType: {}",
                    deliveryOrderNo, itemCode, bizType);

            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, bizType)
                    .last("LIMIT " + ValueAddedConstants.EmployeeLimit.MAX_QUERY_SIZE);

            List<ValueAddedEmployee> employees = valueAddedEmployeeService.list(wrapper);

            // 转换为EmployeeInfo列表
            return employees.stream()
                    .map(this::convertToEmployeeInfo)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to get employee info list for order: {}", deliveryOrderNo, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据itemCode获取员工总数
     * 当itemCode为TAX_SOCIAL_INSURANCE时bizType对应1，TAX_PERSONAL_SALARY/TAX_PERSONAL_OTHER对应bizType为2
     *
     * @param deliveryOrderNo      交付单编号
     * @param valueAddedItemTypeId 增值事项类型ID
     * @return 员工总数
     */
    private Integer getTotalEmployeeCount(String deliveryOrderNo, Integer valueAddedItemTypeId) {
        try {
            // 通过增值事项类型ID获取itemCode
            String itemCode = getItemCodeByTypeId(valueAddedItemTypeId);
            if (StringUtils.isEmpty(itemCode)) {
                log.warn("Cannot find item code for valueAddedItemTypeId: {}", valueAddedItemTypeId);
                return 0;
            }

            // 根据itemCode确定bizType
            Integer bizType = determineBizTypeByItemCode(itemCode);
            if (bizType == null) {
                return 0;
            }

            log.info("Getting employee info list for order: {}, itemCode: {}, bizType: {}",
                    deliveryOrderNo, itemCode, bizType);

            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                    .eq(ValueAddedEmployee::getBizType, bizType);


            // 转换为EmployeeInfo列表
            return valueAddedEmployeeService.count(wrapper);

        } catch (Exception e) {
            log.error("Failed to get employee info list for order: {}", deliveryOrderNo, e);
            return 0;
        }
    }

    /**
     * 根据增值事项类型ID确定bizType
     *
     * @param valueAddedItemTypeId 增值事项类型ID
     * @return bizType，如果无匹配则返回null
     */
    private Integer determineBizTypeByItemTypeId(Integer valueAddedItemTypeId) {
        if (valueAddedItemTypeId == null) {
            return null;
        }

        // 根据增值事项类型ID映射到bizType
        if (ValueAddedConstants.ItemTypeId.SOCIAL_INSURANCE.equals(valueAddedItemTypeId)) {
            return ValueAddedConstants.BizType.SOCIAL_INSURANCE; // 社医保
        } else if (ValueAddedConstants.ItemTypeId.PERSONAL_TAX.equals(valueAddedItemTypeId)) {
            return ValueAddedConstants.BizType.PERSONAL_TAX; // 个税明细
        }

        return null;
    }

    /**
     * 根据增值事项类型ID获取itemCode
     *
     * @param valueAddedItemTypeId 增值事项类型ID
     * @return itemCode，如果无匹配则返回null
     */
    private String getItemCodeByTypeId(Integer valueAddedItemTypeId) {
        if (valueAddedItemTypeId == null) {
            return null;
        }

        try {
            ValueAddedItemType itemType = valueAddedItemTypeService.getById(valueAddedItemTypeId);
            if (itemType != null && !Boolean.TRUE.equals(itemType.getIsDel())) {
                return itemType.getItemCode();
            }
        } catch (Exception e) {
            log.error("Failed to get item code for valueAddedItemTypeId: {}", valueAddedItemTypeId, e);
        }

        return null;
    }

    /**
     * 设置库存信息列表
     *
     * @param vo              交付单VO
     * @param deliveryOrderNo 交付单编号
     */
    private void setStockList(DeliveryOrderVO vo, String deliveryOrderNo) {
        try {
            // 1. 查询库存数据
            List<ValueAddedStock> stockList = valueAddedStockService.getByDeliveryOrderNo(deliveryOrderNo);

            // 2. 转换为VO列表，使用BeanUtils.copyProperties进行转换
            List<ValueAddedStockVO> stockVOList = ValueAddedStockVO.fromEntityList(stockList);
            vo.setStockList(stockVOList);

        } catch (Exception e) {
            log.error("Failed to set stock list for delivery order: {}", deliveryOrderNo, e);
            vo.setStockList(new ArrayList<>());
        }
    }

    /**
     * 根据事项编码确定bizType
     *
     * @param itemCode 事项编码
     * @return bizType，如果无匹配则返回null
     */
    private Integer determineBizTypeByItemCode(String itemCode) {
        if (StringUtils.isEmpty(itemCode)) {
            return null;
        }

        // 根据事项编码映射到bizType
        switch (itemCode) {
            case ValueAddedConstants.ItemCode.TAX_SOCIAL_INSURANCE:
                return ValueAddedConstants.BizType.SOCIAL_INSURANCE; // 社医保
            case ValueAddedConstants.ItemCode.TAX_PERSONAL_SALARY:
            case ValueAddedConstants.ItemCode.TAX_PERSONAL_OTHER:
                return ValueAddedConstants.BizType.PERSONAL_TAX; // 个税
            default:
                log.warn("Unknown item code: {}", itemCode);
                return null;
        }
    }

    /**
     * 将ValueAddedEmployee转换为EmployeeInfo
     *
     * @param employee 员工实体
     * @return EmployeeInfo对象
     */
    private EmployeeInfo convertToEmployeeInfo(ValueAddedEmployee employee) {
        EmployeeInfo info = new EmployeeInfo();
        // 使用BeanUtils复制所有匹配的字段
        BeanUtils.copyProperties(employee, info);
        info.setOperationTypeName(getOperationTypeName(employee.getOperationType(), employee.getBizType()));
        return info;
    }

    /**
     * 获取员工明细的操作方式名
     * 操作方式，社医保/个税明细：1-提醒，2-更正，3-减员；国税账号：1-账号信息，2-会计实名
     *
     * @param operationType 操作方式枚举
     * @param bizType       账号类型
     * @return
     */
    private String getOperationTypeName(Integer operationType, Integer bizType) {
        return ValueAddedOperationType.getNameByForBizType(ValueAddedBizType.getByCode(bizType), operationType);
    }

    /**
     * 设置各类型文件列表（一次查询，按类型分组）
     *
     * @param vo              交付单VO
     * @param deliveryOrderNo 交付单编号
     */
    private void setFilesByType(DeliveryOrderVO vo, String deliveryOrderNo) {
        try {
            // 1. 一次性查询所有文件记录
            LambdaQueryWrapper<ValueAddedFile> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedFile::getDeliveryOrderNo, deliveryOrderNo)
                    .in(ValueAddedFile::getFileType,
                            ValueAddedFileType.DELIVERY_MATERIAL.getCode(),
                            ValueAddedFileType.DELIVERY_ATTACHMENT.getCode(),
                            ValueAddedFileType.STANDARD_ATTACHMENT.getCode())
                    .eq(ValueAddedFile::getIsDel, false)
                    .orderByAsc(ValueAddedFile::getCreateTime);

            List<ValueAddedFile> allFiles = valueAddedFileService.list(wrapper);
            if (allFiles.isEmpty()) {
                log.info("No files found for order: {}", deliveryOrderNo);
                vo.setDeliveryFiles(new ArrayList<>());
                vo.setDeliveryAttachments(new ArrayList<>());
                vo.setStandardAttachments(new ArrayList<>());
                return;
            }

            // 2. 收集所有非空的文件URL用于批量查询
            List<String> fileUrls = allFiles.stream()
                    .map(ValueAddedFile::getFileUrl)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());

//            Map<String, RemoteAliFileDTO> fileInfoMap = new HashMap<>();
//            if (!fileUrls.isEmpty()) {
//                // 3. 批量获取完整的文件信息
//                log.info("Batch getting file info for {} files, delivery order: {}", fileUrls.size(), deliveryOrderNo);
//                R<List<RemoteAliFileDTO>> batchResult = remoteFileService.batchGetFileInfo(fileUrls);
//                if (batchResult.getData() != null) {
//                    fileInfoMap = batchResult.getData().stream()
//                            .collect(Collectors.toMap(RemoteAliFileDTO::getUrl, Function.identity(), (v1, v2) -> v1));
//                }
//            }

            // 4. 按文件类型分组并转换
            Map<Integer, List<ValueAddedFile>> filesByType = allFiles.stream()
                    .collect(Collectors.groupingBy(ValueAddedFile::getFileType));

            // 设置交付材料列表
            vo.setDeliveryFiles(convertToFileDTO(
                    filesByType.getOrDefault(ValueAddedFileType.DELIVERY_MATERIAL.getCode(), new ArrayList<>()),
                    null));

            // 设置交付附件列表
            vo.setDeliveryAttachments(convertToFileDTO(
                    filesByType.getOrDefault(ValueAddedFileType.DELIVERY_ATTACHMENT.getCode(), new ArrayList<>()),
                    null));

            // 设置标准附件列表
            vo.setStandardAttachments(convertToFileDTO(
                    filesByType.getOrDefault(ValueAddedFileType.STANDARD_ATTACHMENT.getCode(), new ArrayList<>()),
                    null));

        } catch (Exception e) {
            log.error("Failed to set files by type for order: {}", deliveryOrderNo, e);
            vo.setDeliveryFiles(new ArrayList<>());
            vo.setDeliveryAttachments(new ArrayList<>());
            vo.setStandardAttachments(new ArrayList<>());
        }
    }

    /**
     * 转换文件列表为DTO列表
     *
     * @param files       文件列表
     * @param fileInfoMap 远程文件信息映射
     * @return DTO列表
     */
    private List<ValueAddedFileDTO> convertToFileDTO(List<ValueAddedFile> files, Map<String, RemoteAliFileDTO> fileInfoMap) {
        List<ValueAddedFileDTO> result = new ArrayList<>();
        for (ValueAddedFile file : files) {
            if (StringUtils.isEmpty(file.getFileUrl())) {
                continue;
            }

//            RemoteAliFileDTO remoteFileInfo = fileInfoMap.get(file.getFileUrl());
            ValueAddedFileDTO fileDTO = new ValueAddedFileDTO();
            fileDTO.setFileId(file.getId());
            fileDTO.setFileName(file.getFileName());
            fileDTO.setUrl(file.getFileUrl());
            fileDTO.setFileSize(file.getFileSize() != null ? file.getFileSize() : 0L);
            fileDTO.setFileUrl(file.getFileUrl());

            // 无需返回长链接
//            if (remoteFileInfo != null) {
//                fileDTO.setFullUrl(remoteFileInfo.getFullUrl());
//                fileDTO.setFileType(remoteFileInfo.getFileType());
//            } else {
//                fileDTO.setFullUrl(file.getFileUrl());
//            }
            result.add(fileDTO);
        }
        return result;
    }

    /**
     * 将ValueAddedEmployee转换为NationalTaxAccountVO
     *
     * @param employee 员工实体
     * @return 国税账号VO
     */
    private NationalTaxAccountVO convertToNationalTaxAccountVO(ValueAddedEmployee employee) {
        NationalTaxAccountVO vo = new NationalTaxAccountVO();
        BeanUtils.copyProperties(employee, vo);
        // 手动设置字段名不匹配的字段
        vo.setAccountNumber(employee.getTaxNumber());
        vo.setPassword(employee.getQueryPassword());
        vo.setOperationTypeName(getNationalTaxOperationTypeName(employee.getOperationType()));
        return vo;
    }

    /**
     * 将ValueAddedEmployee转换为PersonalTaxAccountVO
     *
     * @param employee 员工实体
     * @return 个税账号VO
     */
    private PersonalTaxAccountVO convertToPersonalTaxAccountVO(ValueAddedEmployee employee) {
        PersonalTaxAccountVO vo = new PersonalTaxAccountVO();
        BeanUtils.copyProperties(employee, vo);
        // 手动设置字段名不匹配的字段
        vo.setAccountNumber(employee.getTaxNumber());
        vo.setPassword(employee.getQueryPassword());
        return vo;
    }

    /**
     * 处理账号信息（国税账号和个税账号）
     *
     * @param orderVO         交付单请求VO
     * @param deliveryOrderNo 交付单编号
     */
    private void processAccountInformation(DeliveryOrderUpsertReq orderVO, String deliveryOrderNo) {
        try {
            // 处理国税账号
            if (orderVO.getNationalTaxAccount() != null) {
                processNationalTaxAccount(orderVO.getNationalTaxAccount(), deliveryOrderNo);
            }

            // 处理个税账号
            if (orderVO.getPersonalTaxAccount() != null) {
                processPersonalTaxAccount(orderVO.getPersonalTaxAccount(), deliveryOrderNo);
            }
        } catch (Exception e) {
            log.error("Failed to process account information for delivery order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("处理账号信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理国税账号信息
     *
     * @param nationalTaxAccountVO 国税账号VO
     * @param deliveryOrderNo      交付单编号
     */
    private void processNationalTaxAccount(NationalTaxAccountVO nationalTaxAccountVO, String deliveryOrderNo) {
        try {
            log.info("Processing national tax account for delivery order: {}", deliveryOrderNo);
            // 转换为ValueAddedEmployeeVO
            ValueAddedEmployeeVO employeeVO = convertNationalTaxAccountToEmployeeVO(nationalTaxAccountVO, deliveryOrderNo);
            // 使用策略模式直接处理upsert操作
            ValueAddedEmployeeUpsertStrategy strategy = strategyFactory.getStrategy(employeeVO.getBizType());
            Long employeeId = strategy.upsert(employeeVO);
            log.info("National tax account processed successfully for delivery order: {}, employee ID: {}",
                    deliveryOrderNo, employeeId);
        } catch (Exception e) {
            log.error("Failed to process national tax account for delivery order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("处理国税账号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理个税账号信息
     *
     * @param personalTaxAccountVO 个税账号VO
     * @param deliveryOrderNo      交付单编号
     */
    private void processPersonalTaxAccount(PersonalTaxAccountVO personalTaxAccountVO, String deliveryOrderNo) {
        try {
            log.info("Processing personal tax account for delivery order: {}", deliveryOrderNo);
            // 转换为ValueAddedEmployeeVO
            ValueAddedEmployeeVO employeeVO = convertPersonalTaxAccountToEmployeeVO(personalTaxAccountVO, deliveryOrderNo);
            // 使用策略模式直接处理upsert操作
            ValueAddedEmployeeUpsertStrategy strategy = strategyFactory.getStrategy(employeeVO.getBizType());
            Long employeeId = strategy.upsert(employeeVO);

            log.info("Personal tax account processed successfully for delivery order: {}, employee ID: {}",
                    deliveryOrderNo, employeeId);
        } catch (Exception e) {
            log.error("Failed to process personal tax account for delivery order: {}, error: {}",
                    deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("处理个税账号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建基础的ValueAddedEmployeeVO对象
     *
     * @param deliveryOrderNo     交付单编号
     * @param bizType             业务类型
     * @param defaultEmployeeName 默认员工姓名
     * @return 基础的ValueAddedEmployeeVO对象
     */
    private ValueAddedEmployeeVO buildBaseEmployeeVO(String deliveryOrderNo, Integer bizType, String defaultEmployeeName) {
        ValueAddedEmployeeVO employeeVO = new ValueAddedEmployeeVO();
        employeeVO.setDeliveryOrderNo(deliveryOrderNo);
        employeeVO.setBizType(bizType);
        employeeVO.setEntryType(2); // 单个新增
        employeeVO.setEmployeeName(defaultEmployeeName);
        return employeeVO;
    }

    /**
     * 将国税账号VO转换为ValueAddedEmployeeVO
     *
     * @param nationalTaxAccountVO 国税账号VO
     * @param deliveryOrderNo      交付单编号
     * @return ValueAddedEmployeeVO
     */
    private ValueAddedEmployeeVO convertNationalTaxAccountToEmployeeVO(NationalTaxAccountVO nationalTaxAccountVO, String deliveryOrderNo) {
        // 构建基础EmployeeVO对象
        String defaultEmployeeName = StringUtils.isNotEmpty(nationalTaxAccountVO.getRealNameAgent())
                ? nationalTaxAccountVO.getRealNameAgent() : "国税账号经办人";
        ValueAddedEmployeeVO employeeVO = buildBaseEmployeeVO(deliveryOrderNo,
                ValueAddedConstants.BizType.NATIONAL_TAX_ACCOUNT, defaultEmployeeName);
        BeanUtils.copyProperties(nationalTaxAccountVO, employeeVO);
        // 处理字段名不匹配的特殊映射
        employeeVO.setTaxNumber(nationalTaxAccountVO.getAccountNumber()); // accountNumber → taxNumber
        employeeVO.setQueryPassword(nationalTaxAccountVO.getPassword()); // password → queryPassword
        // 设置操作方式默认值（如果为空）
        if (employeeVO.getOperationType() == null) {
            employeeVO.setOperationType(1); // 默认为会计实名
        }

        return employeeVO;
    }

    /**
     * 将个税账号VO转换为ValueAddedEmployeeVO
     *
     * @param personalTaxAccountVO 个税账号VO
     * @param deliveryOrderNo      交付单编号
     * @return ValueAddedEmployeeVO
     */
    private ValueAddedEmployeeVO convertPersonalTaxAccountToEmployeeVO(PersonalTaxAccountVO personalTaxAccountVO, String deliveryOrderNo) {
        // 构建基础EmployeeVO对象
        String defaultEmployeeName = StringUtils.isNotEmpty(personalTaxAccountVO.getRealNameAgent())
                ? personalTaxAccountVO.getRealNameAgent() : "个税账号经办人";
        ValueAddedEmployeeVO employeeVO = buildBaseEmployeeVO(deliveryOrderNo,
                ValueAddedConstants.BizType.PERSONAL_TAX_ACCOUNT, defaultEmployeeName);

        BeanUtils.copyProperties(personalTaxAccountVO, employeeVO);
        employeeVO.setTaxNumber(personalTaxAccountVO.getAccountNumber()); // accountNumber → taxNumber
        employeeVO.setQueryPassword(personalTaxAccountVO.getPassword()); // password → queryPassword

        // 设置操作方式默认值（如果为空）
        if (employeeVO.getOperationType() == null) {
            employeeVO.setOperationType(1);
        }
        return employeeVO;
    }


    /**
     * 将 ValueAddedItemType 实体转换为 ValueAddedItemTypeVO
     *
     * @param itemType 增值事项类型实体
     * @return 增值事项类型VO对象
     */
    private ValueAddedItemTypeVO convertToItemTypeVO(ValueAddedItemType itemType) {
        if (itemType == null) {
            return null;
        }
        ValueAddedItemTypeVO vo = new ValueAddedItemTypeVO();
        BeanUtils.copyProperties(itemType, vo);
        return vo;
    }

    /**
     * 校验特定itemCode的人员明细数据
     * 当itemCode为TAX_PERSONAL_OTHER、TAX_PERSONAL_SALARY、TAX_SOCIAL_INSURANCE时，
     * 校验employee表是否存在当前deliveryOrderNo的数据，如果完全不存在则报错
     *
     * @param order 增值交付单对象
     * @throws RuntimeException 当人员明细不能为空时抛出
     */
    private void validateEmployeeDataForPersonalTaxItems(ValueAddedDeliveryOrder order) {
        try {
            log.info("Validating employee data for personal tax items, deliveryOrderNo: {}, valueAddedItemTypeId: {}",
                    order.getDeliveryOrderNo(), order.getValueAddedItemTypeId());

            // 根据增值事项类型ID获取itemCode
            String itemCode = getItemCodeByTypeId(order.getValueAddedItemTypeId());
            if (StringUtils.isEmpty(itemCode)) {
                log.debug("ItemCode is empty for valueAddedItemTypeId: {}, skip validation", order.getValueAddedItemTypeId());
                return;
            }

            // 判断是否为需要校验的itemCode
            boolean needValidation = ValueAddedConstants.ItemCode.TAX_PERSONAL_SALARY.equals(itemCode) ||
                    ValueAddedConstants.ItemCode.TAX_SOCIAL_INSURANCE.equals(itemCode);

            if (!needValidation) {
                return;
            }
            // 根据itemCode确定bizType
            Integer bizType = determineBizTypeByItemCode(itemCode);
            if (bizType == null) {
                log.warn("Cannot determine bizType for itemCode: {}", itemCode);
                return;
            }

            // 查询对应的员工数据
            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, order.getDeliveryOrderNo())
                    .eq(ValueAddedEmployee::getBizType, bizType)
                    .last("LIMIT 1"); // 只需要检查是否存在，不需要查询全部

            ValueAddedEmployee existingEmployee = valueAddedEmployeeService.getOne(wrapper);

            if (existingEmployee == null) {
                log.error("Employee data validation failed: no employee found for deliveryOrderNo: {}, bizType: {}",
                        order.getDeliveryOrderNo(), bizType);
                throw new RuntimeException("人员明细不能为空");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error during employee data validation for deliveryOrderNo: {}",
                    order.getDeliveryOrderNo(), e);
            throw new RuntimeException("校验人员明细数据时发生错误: " + e.getMessage(), e);
        }
    }


    /**
     * 保存业务日志的公共方法（兼容旧版本）
     *
     * @param businessId 业务ID
     * @param operType   操作类型
     * @param content    操作内容对象
     */
    private void saveUpsertBusinessLog(Long businessId, String operType, DeliveryOrderUpsertReq content) {
        try {
            OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(content.getDeptId(), SecurityUtils.getUserId());

            // 创建字段覆盖映射，重写AccountingInfoVO的显示值
            Map<FieldReference<DeliveryOrderUpsertReq, ?>, Function<DeliveryOrderUpsertReq, String>> fieldOverrides = new HashMap<>();
            fieldOverrides.put(DeliveryOrderUpsertReq::getAccountingInfo, this::formatAccountingInfoForLog);
            fieldOverrides.put(DeliveryOrderUpsertReq::getTaxpayerType, (c) -> TaxpayerTypeEnum.getDescriptionByCode(c.getTaxpayerType()));
            fieldOverrides.put(DeliveryOrderUpsertReq::getItemName, (c) -> valueAddedItemTypeService.getById(c.getValueAddedItemTypeId()).getItemName());
            fieldOverrides.put(DeliveryOrderUpsertReq::getAccountingMethod, (c) -> AccountingMethodEnum.getDescriptionByCode(c.getAccountingMethod()));
            // 国税账号字段覆盖：只显示operationTypeName，不显示operationType
            fieldOverrides.put(DeliveryOrderUpsertReq::getNationalTaxAccount, this::formatNationalTaxAccountForLog);

            String baseContent = LogUtils.toLogString(content, fieldOverrides,
                    DeliveryOrderUpsertReq::getId,
                    DeliveryOrderUpsertReq::getCreateUid,
                    DeliveryOrderUpsertReq::getInitiateDeptId,
                    DeliveryOrderUpsertReq::getAccountingDeptId,
                    DeliveryOrderUpsertReq::getBusinessDeptId,
                    DeliveryOrderUpsertReq::getBusinessTopDeptId,
                    DeliveryOrderUpsertReq::getDeliveryFiles,
                    DeliveryOrderUpsertReq::getValueAddedItemTypeId,
                    DeliveryOrderUpsertReq::getStatus);

            // 处理文件字段，转换为JSON字符串放入operImages
            String operImages = "";
            if (content.getDeliveryFiles() != null && !content.getDeliveryFiles().isEmpty()) {
                operImages = JSONObject.toJSONString(content.getDeliveryFiles());
            }
            asyncLogService.saveBusinessLog(new BusinessLogDTO()
                    .setBusinessId(businessId)
                    .setBusinessType(BusinessLogBusinessType.DELIVERY_ORDER.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType(operType)
                    .setOperName(operateUserInfo.getOperName())
                    .setOperUserId(operateUserInfo.getUserId())
                    .setOperContent(baseContent)
                    .setOperImages(operImages));
        } catch (Exception e) {
            log.error("{}操作日志记录失败: {}", operType, e.getMessage(), e);
        }
    }

    /**
     * 保存状态变更操作日志
     *
     * @param businessId    业务ID
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态
     * @param request       状态变更请求
     */
    private void saveStatusChangeBusinessLog(Long businessId,
                                             ValueAddedDeliveryOrderStatus currentStatus,
                                             ValueAddedDeliveryOrderStatus targetStatus,
                                             StatusChangeRequestDTO request) {
        try {
            OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(request.getDeptId(), SecurityUtils.getUserId());
            // 使用枚举获取操作类型的中文描述
            String operTypeDescription = ValueAddedOperTypeEnum.getByCode(request.getOperTypeName()).getDescription();

            // 1. 创建字段覆盖映射，用于自定义targetStatus的显示内容
            Map<FieldReference<StatusChangeRequestDTO, ?>, Function<StatusChangeRequestDTO, String>> fieldOverrides = new HashMap<>();

            // 添加入账方式枚举转换
            fieldOverrides.put(StatusChangeRequestDTO::getAccountingMethod, (c) -> AccountingMethodEnum.getDescriptionByCode(c.getAccountingMethod()));

            // 4. 使用字段覆盖和忽略功能生成日志内容
            // 注意：不要忽略dealResult字段，因为它需要被覆盖
            String baseContent = LogUtils.toLogString(request, fieldOverrides,
                    StatusChangeRequestDTO::getDeliveryFiles,
                    StatusChangeRequestDTO::getDeliveryAttachments,
                    StatusChangeRequestDTO::getStandardAttachments,
                    StatusChangeRequestDTO::getDeliveryOrderNo,
                    StatusChangeRequestDTO::getOperTypeName,
                    StatusChangeRequestDTO::getTargetStatus,
                    StatusChangeRequestDTO::getCreditCode,
                    StatusChangeRequestDTO::getOperationAttachments);

            // 处理文件字段，合并所有文件列表并转换为JSON字符串
            String operImages = "";
            List<Object> allFiles = new ArrayList<>();
            if (request.getDeliveryFiles() != null) {
                allFiles.addAll(request.getDeliveryFiles());
            }
            if (request.getDeliveryAttachments() != null) {
                allFiles.addAll(request.getDeliveryAttachments());
            }
            if (request.getStandardAttachments() != null) {
                allFiles.addAll(request.getStandardAttachments());
            }
            if (request.getOperationAttachments() != null) {
                allFiles.addAll(request.getOperationAttachments());
            }
            if (!allFiles.isEmpty()) {
                operImages = JSONObject.toJSONString(allFiles);
            }

            asyncLogService.saveBusinessLog(new BusinessLogDTO()
                    .setBusinessId(businessId)
                    .setBusinessType(BusinessLogBusinessType.DELIVERY_ORDER.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType(operTypeDescription)
                    .setOperName(operateUserInfo.getOperName())
                    .setOperUserId(operateUserInfo.getUserId())
                    .setOperContent(baseContent)
                    .setOperImages(operImages));
        } catch (Exception e) {
            log.error("状态变更操作日志记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 分派操作记录
     *
     * @param businessId  业务ID
     * @param operContent 操作记录内容
     */
    private void saveDispatchBusinessLog(Long businessId, DispatchRequestDTO request, String operContent) {
        try {
            OperateUserInfoDTO operateUserInfo = customerServiceDocHandoverService.getOperateUserInfo(request.getDeptId(), SecurityUtils.getUserId());

            asyncLogService.saveBusinessLog(new BusinessLogDTO()
                    .setBusinessId(businessId)
                    .setBusinessType(BusinessLogBusinessType.DELIVERY_ORDER.getCode())
                    .setDeptId(operateUserInfo.getDeptId())
                    .setOperType("分派")
                    .setOperName(operateUserInfo.getOperName())
                    .setOperUserId(operateUserInfo.getUserId())
                    .setOperContent(operContent));
        } catch (Exception e) {
            log.error("状态变更操作日志记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存附件文件到ValueAddedFile表
     *
     * @param request 状态变更请求
     */
    private void saveAttachmentFiles(StatusChangeRequestDTO request) {
        try {
            String deliveryOrderNo = request.getDeliveryOrderNo();
            List<ValueAddedFile> allFileRecords = new ArrayList<>();

            if (ValueAddedOperTypeEnum.REJECT.getCode().equals(request.getOperTypeName())) {
                if (!ObjectUtils.isEmpty(request.getOperationAttachments())) {
                    List<ValueAddedFile> operationFileRecords = buildFileRecords(
                            request.getOperationAttachments(), deliveryOrderNo, ValueAddedFileType.DELIVERY_MATERIAL);
                    allFileRecords.addAll(operationFileRecords);
                }
            }

            // 收集交付材料列表 (fileType=1)
            if (request.getDeliveryFiles() != null) {
                valueAddedFileService.deleteFileByDeliveryOrderNoAndFileTypes(deliveryOrderNo, ValueAddedFileType.DELIVERY_MATERIAL.getCode());
                if (!ObjectUtils.isEmpty(request.getDeliveryFiles())) {
                    List<ValueAddedFile> deliveryFileRecords = buildFileRecords(
                            request.getDeliveryFiles(), deliveryOrderNo, ValueAddedFileType.DELIVERY_MATERIAL);
                    allFileRecords.addAll(deliveryFileRecords);
                }
            }

            // 收集交付附件列表 (fileType=6)
            if (request.getDeliveryAttachments() != null) {
                valueAddedFileService.deleteFileByDeliveryOrderNoAndFileTypes(deliveryOrderNo, ValueAddedFileType.DELIVERY_ATTACHMENT.getCode());
                if (!request.getDeliveryAttachments().isEmpty()) {
                    List<ValueAddedFile> deliveryAttachmentRecords = buildFileRecords(
                            request.getDeliveryAttachments(), deliveryOrderNo, ValueAddedFileType.DELIVERY_ATTACHMENT);
                    allFileRecords.addAll(deliveryAttachmentRecords);
                }
            }

            // 收集标准附件列表 (fileType=5)
            if (request.getStandardAttachments() != null) {
                valueAddedFileService.deleteFileByDeliveryOrderNoAndFileTypes(deliveryOrderNo, ValueAddedFileType.STANDARD_ATTACHMENT.getCode());
                if (!request.getStandardAttachments().isEmpty()) {
                    List<ValueAddedFile> standardFileRecords = buildFileRecords(
                            request.getStandardAttachments(), deliveryOrderNo, ValueAddedFileType.STANDARD_ATTACHMENT);
                    allFileRecords.addAll(standardFileRecords);
                }
            }


            // 批量保存所有文件记录
            if (!allFileRecords.isEmpty()) {
                boolean batchSaveResult = valueAddedFileService.saveBatch(allFileRecords);
                if (!batchSaveResult) {
                    throw new RuntimeException("批量保存文件记录失败");
                }
            }

        } catch (Exception e) {
            log.error("Failed to save attachment files for order: {}, error: {}", request.getDeliveryOrderNo(), e.getMessage(), e);
            throw new RuntimeException("保存附件文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存交付文件
     *
     * @param deliveryFiles   交付文件列表
     * @param deliveryOrderNo 交付单编号
     */
    private void saveDeliveryFiles(List<ValueAddedFileDTO> deliveryFiles, String deliveryOrderNo) {
        try {
            if (deliveryFiles == null || deliveryFiles.isEmpty()) {
                return;
            }
            // 构建文件记录
            List<ValueAddedFile> fileRecords = buildFileRecords(
                    deliveryFiles, deliveryOrderNo, ValueAddedFileType.DELIVERY_MATERIAL);
            if (!fileRecords.isEmpty()) {
                boolean batchSaveResult = valueAddedFileService.saveBatch(fileRecords);
                if (!batchSaveResult) {
                    throw new RuntimeException("批量保存交付文件记录失败");
                }
            }
        } catch (Exception e) {
            log.error("Failed to save delivery files for order: {}, error: {}", deliveryOrderNo, e.getMessage(), e);
            throw new RuntimeException("保存交付文件失败: " + e.getMessage());
        }
    }

    /**
     * 构建文件记录列表
     * 只取 fileName、fileSize、fileUrl 三个字段构建 ValueAddedFile 对象
     *
     * @param fileList        文件DTO列表
     * @param deliveryOrderNo 交付单编号
     * @param fileType        文件类型
     * @return 文件记录列表
     */
    private List<ValueAddedFile> buildFileRecords(List<ValueAddedFileDTO> fileList, String deliveryOrderNo, ValueAddedFileType fileType) {
        return fileList.stream()
                .filter(fileDTO -> fileDTO != null) // 过滤空对象
                .map(fileDTO -> {
                    ValueAddedFile fileRecord = new ValueAddedFile();
                    fileRecord.setDeliveryOrderNo(deliveryOrderNo);
                    // 只取 fileName、fileSize、fileUrl 三个字段
                    fileRecord.setFileName(fileDTO.getFileName());
                    // 使用 Optional 优雅处理 fileUrl 和 url 的选择
                    fileRecord.setFileUrl(Optional.ofNullable(fileDTO.getFileUrl())
                            .filter(url -> !url.isEmpty())
                            .orElse(fileDTO.getUrl()));
                    fileRecord.setFileSize(fileDTO.getFileSize());
                    fileRecord.setFileType(fileType.getCode());
                    fileRecord.setIsDel(false);
                    fileRecord.setStatus(ValueAddedProcessStatus.COMPLETED.getCode()); // 处理完成状态
                    return fileRecord;
                })
                .collect(Collectors.toList());
    }

    /**
     * 填充发起部门路径
     * 根据order中的initiate_dept_id获取部门路径并设置到order中
     *
     * @param order 交付单对象
     */
    /**
     * 计算发起部门路径
     *
     * @param initiateDeptId 发起部门ID
     * @return 部门路径列表
     */
    private List<Long> calculateInitiateDeptPath(Long initiateDeptId) {
        try {
            // 如果发起部门ID为空，返回空路径
            if (Objects.isNull(initiateDeptId)) {
                return new ArrayList<>();
            }
            // 获取所有部门信息
            Map<Long, SysDept> deptMap = remoteDeptService.getAllDept()
                    .getDataThrowException()
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));

            // 根据发起部门ID获取部门路径
            List<Long> initiateDeptPath = getDeptIdPath(initiateDeptId, deptMap);
            return initiateDeptPath;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 获取部门路径
     * 根据部门ID获取从根部门到当前部门的完整路径
     *
     * @param deptId  部门ID
     * @param deptMap 部门映射表
     * @return 部门路径列表
     */
    private List<Long> getDeptIdPath(Long deptId, Map<Long, SysDept> deptMap) {
        if (Objects.isNull(deptId)) {
            return new ArrayList<>();
        }

        SysDept dept = deptMap.get(deptId);
        if (Objects.isNull(dept) || !Objects.equals("0", dept.getDelFlag())) {
            return new ArrayList<>();
        }

        List<Long> ids = Arrays.stream(dept.getAncestors().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        ids.remove(0L); // 移除根节点0
        ids.add(dept.getDeptId()); // 添加当前部门ID
        return ids;
    }

    /**
     * 格式化AccountingInfoVO用于日志显示
     * mainType取描述，subTypes取中文值数组
     *
     * @param content 交付单内容
     * @return 格式化后的字符串，如：非标账户：["高新账", "凭票入账"]
     */
    private String formatAccountingInfoForLog(DeliveryOrderUpsertReq content) {
        try {
            AccountingInfoVO accountingInfo = content.getAccountingInfo();
            if (accountingInfo == null) {
                return "";
            }

            StringBuilder result = new StringBuilder();

            // mainType取描述
            if (accountingInfo.getMainType() != null) {
                result.append(accountingInfo.getMainType().getDescription());
            }

            // subTypes取中文描述值
            if (accountingInfo.getSubTypes() != null && !accountingInfo.getSubTypes().isEmpty()) {
                List<String> formattedSubTypes = new ArrayList<>();
                for (String subType : accountingInfo.getSubTypes()) {
                    String label = dictConversionService.getDictLabel(AccountMainType.NON_STANDARD.name(), subType);
                    formattedSubTypes.add(label);
                }
                result.append("：").append(String.join(",", formattedSubTypes));
            }

            return result.toString();
        } catch (Exception e) {
            log.error("格式化AccountingInfo失败: {}", e.getMessage(), e);
            return ""; // 如果失败，返回空字符串
        }
    }

    /**
     * 根据国税账号操作类型代码获取中文名称
     *
     * @param operationType 操作类型代码
     * @return 操作类型中文名称
     */
    private String getNationalTaxOperationTypeName(Integer operationType) {
        if (operationType == null) {
            return "未知操作";
        }

        // 根据国税账号业务类型的操作类型映射
        switch (operationType) {
            case 1:
                return ValueAddedOperationType.ACCOUNTING_REAL_NAME.getName(); // "账号信息"
            case 2:
                return ValueAddedOperationType.REMOTE_REAL_NAME.getName(); // "会计实名"
            default:
                return "未知操作(" + operationType + ")";
        }
    }

    /**
     * 格式化国税账号信息用于日志显示
     * 只显示operationTypeName，隐藏operationType字段
     *
     * @param content 交付单内容
     * @return 格式化后的国税账号日志字符串
     */
    private String formatNationalTaxAccountForLog(DeliveryOrderUpsertReq content) {
        try {
            NationalTaxAccountVO nationalTaxAccount = content.getNationalTaxAccount();
            if (nationalTaxAccount == null) {
                return "";
            }
            // 创建国税账号的字段覆盖映射：用operationTypeName替换operationType
            Map<FieldReference<NationalTaxAccountVO, ?>, Function<NationalTaxAccountVO, String>> overrides = new HashMap<>();
            overrides.put(NationalTaxAccountVO::getOperationType, vo -> vo.getOperationTypeName());

            LogStringConfig config = LogStringConfig.builder()
                    .format(OutputFormat.KEY_VALUE)
                    .skipEmptyObjects(true)
                    .skipNullValues(true)
                    .skipEmptyStrings(true)
                    .enableFieldOverrides(true)
                    .build();
            return LogUtils.toLogString(nationalTaxAccount, config,overrides);
        } catch (Exception e) {
            log.error("格式化NationalTaxAccount失败: {}", e.getMessage(), e);
            return ""; // 如果失败，返回空字符串
        }
    }

}
