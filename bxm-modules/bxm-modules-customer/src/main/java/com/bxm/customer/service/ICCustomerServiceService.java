package com.bxm.customer.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.domain.PageResult;
import com.bxm.common.core.enums.TaxType;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServiceBankAccount;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.dto.*;
import com.bxm.customer.domain.dto.customerService.CustomerServiceDeliverMattersDTO;
import com.bxm.customer.domain.dto.customerService.CustomerServiceOtherMattersDTO;
import com.bxm.customer.domain.dto.customerService.CustomerServicePeriodMonthInfoDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferAllInfoDTO;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferInfoDTO;
import com.bxm.customer.domain.vo.*;
import com.bxm.customer.domain.vo.customerService.CustomerServiceDeliverMattersVO;
import com.bxm.customer.domain.vo.customerService.CustomerServiceOtherMattersVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.thirdpart.api.domain.RemoteCompanyInfoDTO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 客户服务Service接口
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
public interface ICCustomerServiceService extends IService<CCustomerService> {
    /**
     * 查询客户服务
     *
     * @param id 客户服务主键
     * @return 客户服务
     */
    public CCustomerService selectCCustomerServiceById(Long id);

    /**
     * 查询客户服务列表
     *
     * @param cCustomerService 客户服务
     * @return 客户服务集合
     */
    public List<CCustomerService> selectCCustomerServiceList(CCustomerService cCustomerService);

    /**
     * 新增客户服务
     *
     * @param cCustomerService 客户服务
     * @return 结果
     */
    public int insertCCustomerService(CCustomerService cCustomerService);

    /**
     * 修改客户服务
     *
     * @param cCustomerService 客户服务
     * @return 结果
     */
    public int updateCCustomerService(CCustomerService cCustomerService);

    /**
     * 批量删除客户服务
     *
     * @param ids 需要删除的客户服务主键集合
     * @return 结果
     */
    public CommonOperateDTO deleteCCustomerServiceByIds(Long[] ids);

    /**
     * 删除客户服务信息
     *
     * @param id 客户服务主键
     * @return 结果
     */
    public int deleteCCustomerServiceById(Long id);

    IPage<CustomerServiceDTO> customerServiceList(Long deptId, CustomerServiceSearchVO vo);

    List<CommonDeptCountDTO> customerServiceAccountingCountList(Long deptId, CustomerServiceSearchVO vo);

    List<CommonDeptCountDTO> customerServiceAdvisorCountList(Long deptId, CustomerServiceSearchVO vo);

    List<CustomerServiceDTO> sameCreditCodeServiceList(Long customerServiceId, Long deptId);

    IPage<CustomerServicePeriodMonthDTO> customerServicePeriodMonthList(Long deptId, CustomerServicePeriodMonthSearchVO vo);

    List<CommonDeptCountDTO> customerServicePeriodMonthAccountingDeptCountList(Long deptId, CustomerServicePeriodMonthSearchVO vo);

    List<CommonDeptCountDTO> customerServicePeriodMonthAdvisorDeptCountList(Long deptId, CustomerServicePeriodMonthSearchVO vo);

    Boolean updateCustomerServicePeriodMonthServiceInfo(Long deptId, UpdatePeriodMonthDTO dto);

    CustomerServiceDetailDTO customerServiceDetail(Long id, Long deptId);

    String getAdvisorDeptInfo(Integer deptType, String businessTopDeptName, String businessDeptName, String advisorDeptName, String advisorEmployeeName);

    String getAccountingDeptInfo(Integer deptType, String accountingTopDeptName, String accountingDeptName, String accountingEmployeeName);

    CustomerServiceDetailDTO customerServiceDetailNoDataScope(Long id, Long deptId);

    void addCustomerService(CustomerServiceDetailDTO dto, Long deptId);

    void updateCustomerService(CustomerServiceDetailDTO dto, Long deptId);

    void updateCustomerServiceTask(CustomerServiceDetailDTO dto);

    List<CustomerServiceSysAccountDTO> sysAccountList(Long id, Long deptId);

    void addSysAccount(CustomerServiceSysAccountDTO dto, Long deptId);

    void remoteAddSysAccount(RemoteCustomerServiceSysAccountVO vo);

    void updateSysAccount(CustomerServiceSysAccountDTO dto, Long deptId);

    void deleteSysAccount(Long[] ids);

    List<CustomerServiceTaxTypeCheckVO> customerServiceTaxTypeCheckList(Long customerServiceId);

    Boolean checkTaxTypeExists(String taxType);

    void editCustomerServiceTaxTypeCheck(CustomerServiceTaxTypeCheckDTO dto);

    List<CustomerServiceTaxTypeCheckVO> customerServicePeriodTaxTypeCheckList(Long customerServicePeriodMonthId);

    void editCustomerServicePeriodTaxTypeCheck(CustomerServicePeriodTaxTypeCheckDTO dto);

    List<CustomerServiceStatisticDTO> customerServiceAdvisorDeptStatistic(Long deptId, Long userId);

    List<CustomerServiceStatisticDTO> customerServiceAccountingDeptStatistic(Long deptId, Long userId);

    //获取 结束服务/移除 操作时的前置数据
    PreEndServiceInfoDTO preEndServiceInfo(Long customerServiceId);

    CommonOperateDTO endService(CustomerServiceEndVO vo);

    //结束服务、移出-V2
    CommonOperateDTO endServiceV2(CustomerServiceEndV2VO vo);

    CommonOperateDTO frozeService(CustomerServiceFrozeVO vo);

    //获取解冻账期范围
    UnFrozeServicePeriodDTO getUnFrozeServicePeriod(Long customerServiceId);

    CommonOperateDTO unFrozeService(CustomerServiceUnFrozeVO vo);

    //解冻-V2
    CommonOperateDTO unFrozeServiceV2(CustomerServiceUnFrozeV2VO vo);

    CommonOperateDTO changeBusinessDept(CustomerServiceChangeBusinessDeptVO vo);

    CommonOperateDTO dispatchAdvisor(CustomerServiceDispatchVO vo);

    CommonOperateDTO dispatchAccounting(CustomerServiceDispatchVO vo);

    CommonOperateDTO restartService(CustomerServiceRestartVO vo);

    //重启服务
    CommonOperateDTO restartServiceV2(CustomerServiceRestartVO vo);

    void customerServiceChangeNameForce(CustomerServiceChangeNameVO vo);

    IPage<CustomerServiceWaitItemDTO> customerServiceWaitItemList(Integer itemType, Integer jumpType, String keyWord, Long deptId, Long queryDeptId, String deptIds, String tagName, Integer tagIncludeFlag, String advisorDeptEmployeeName, String accountingDeptEmployeeName, Integer taxType, Integer pageNum, Integer pageSize);

    void notDispatch(CommonIdVO vo, Long deptId);

    CustomerServiceWorkbenchDTO customerServiceComprehensiveStatistic(Long deptId, Long userId);

    CustomerServiceCountDTO customerServiceAdvisorComprehensiveStatistic(Long deptId, Long headerDeptId, Long userId);

    //type=1是顾问的统计，type=2是会计的统计，只会存在1或2
    CustomerServiceCountV2DTO customerServiceAdvisorComprehensiveStatisticV2(Long deptId, Long headerDeptId, Long userId, Integer type);

    CustomerServiceCountDTO customerServiceAccountingComprehensiveStatistic(Long deptId, Long headerDeptId, Long userId);

    CustomerServiceCountV2DTO customerServiceAccountingComprehensiveStatisticV2(Long deptId, Long headerDeptId, Long userId);

    //获取本期移动标签
    List<String> unusualActionTagThisMonth(Long deptId, Long userId, Long customerServiceId);

    //获取本期移动标签-批量
    Map<Long, List<String>> unusualActionTagThisMonthBatch(Long deptId, Long userId, List<Long> customerServiceIds);

    //获取上期移动标签
    List<String> unusualActionTagPreMonth(Long deptId, Long userId, Long customerServiceId);

    //获取上期移动标签-批量
    Map<Long, List<String>> unusualActionTagPreMonthBatch(Long deptId, Long userId, List<Long> customerServiceIds);

    CustomerServiceIncomeDTO customerServiceIncome(Long customerServiceId);

    CustomerServiceStartYearDTO customerServiceStartYear(Long customerServiceId);

    CustomerServiceYearCollectDTO customerServiceYearCollect(Long customerServiceId, Integer year);

    List<CustomerServiceMonthPeriodDTO> customerServiceMonthPeriodList(Long customerServiceId, Integer year);

    List<CustomerServiceMonthPeriodV2DTO> customerServiceMonthPeriodListV2(Long customerServiceId, Integer year);

    CustomerServiceMonthPeriodDetailDTO customerServiceMonthPeriodDetail(Long customerServiceId, Long customerServicePeriodMonthId, Long deptId);

    void updateBusinessInformation(Long customerServiceId);

    CommonOperateDTO confirmChangeName(CommonIdVO vo);

    Boolean checkHasCustomerService(Long deptId);

    CustomerServiceDeptCapacityDTO getCustomerServiceDeptCapacityInfo(Long deptId);

    void editCustomerServiceTag(CustomerServiceTagVO vo);

    Boolean checkServiceNumberExists(String serviceNumber, Long id);

    List<TagDTO> tagListSelect();

    List<RemoteCompanyInfoDTO> getCompanyInfoByKeyWord(String keyWord);

    RemoteCompanyInfoDTO getCompanyInfo(Long customerServiceId);

    void createPeriod(String param);

    void customerServiceChangeDone(String jobParam);

    CustomerSearchResultDTO customerSearch(Long deptId, String keyWord);

    CommonOperateDTO changeAccountingTopDept(CustomerServiceChangeAccountingDeptVO vo);

    List<CCustomerService> serviceSelect(String keyWord, Long deptId);

    IPage<CustomerServiceIncomeExcessDTO> customerServiceWarningList(String keyWord, Long deptId, List<Long> queryDeptIds, Integer deptType, String tagName, Integer tagIncludeFlag, List<Long> advisorSearchDeptIds, List<Long> accountingSearchDeptIds, Integer pageNum, Integer pageSize);

    Long customerServiceWarningCount(String keyWord, Long deptId, List<Long> queryDeptIds, Integer deptType, String tagName, Integer tagIncludeFlag, List<Long> searchDeptIds, Integer pageNum, Integer pageSize);

    CustomerServiceDeliverCountDTO customerServiceDeliverStatistic(Long deptId, Long queryDeptId, String deptIds, Integer deliverType);

    IPage<CustomerDeliverMiniDTO> customerDeliverMiniList(Long deptId, Long queryDeptId, String deptIdStr, Integer deliverType, Integer deliverStatus, String customerName, String tagName, Integer tagIncludeType, Integer period, Long advisorDeptId, Long accountingDeptId, Integer status, String customerServiceTagName, Integer customerServiceTagIncludeFlag, Long customerServiceAdvisorDeptId, Long customerServiceAccountingDeptId, Integer customerServiceTaxType, String periodTagName, Integer periodTagIncludeFlag, Long periodAdvisorDeptId, Long periodAccountingDeptId, Integer periodTaxType, String taxCheckType, Integer pageNum, Integer pageSize);

    List<CustomerDeliverMiniDTO> remoteCustomerDeliverMiniList(Long deptId, Long queryDeptId, String deptIdStr, Integer deliverType, Integer deliverStatus, String customerName, String tagName, Integer tagIncludeType, Integer period, Long advisorDeptId, Long accountingDeptId, String customerServiceTagName, Integer customerServiceTagIncludeFlag, Long customerServiceAdvisorDeptId, Long customerServiceAccountingDeptId, Integer customerServiceTaxType, String periodTagName, Integer periodTagIncludeFlag, Long periodAdvisorDeptId, Long periodAccountingDeptId, Integer periodTaxType, String taxCheckType);

    List<CustomerDeliverMiniDTO> medicalSocialWaitDealList(Long deptId, Integer operType, Integer period);

    List<CustomerDeliverMiniDTO> updateFileWaitDealList(Long deptId, Integer operType, Integer period, Integer deliverType);

    Integer countCustomerDeliverMiniList(Long deptId, Long queryDeptId, List<Long> queryDeptIds, Integer queryDeptType, Integer deliverType, Integer deliverStatus, String customerName, String tagName, Integer tagIncludeType, Integer period, Long advisorDeptId, Long accountingDeptId);

    List<CCustomerService> getCreditCodeByListCreditCode(List<String> creditCodes);

    List<CCustomerService> selectByTaxType(TaxType taxType);

    IPage<CustomerServiceDTO> thisMonthEndList(String keyWord, Long deptId, Long queryDeptId, String tagName, Integer tagIncludeFlag, Integer deptType, String advisorDeptEmployeeName, String accountingDeptEmployeeName, Integer pageNum, Integer pageSize);

    List<CustomerServiceBankAccountDTO> getCustomerServiceBankAccountList(Long id);

    void deleteCustomerServiceBankAccount(CommonIdVO vo, Long deptId);

    void addCustomerServiceBankAccount(CustomerServiceBankAccountDTO vo, Long deptId);

    void updateBankPaymentResultByCreateBank(CustomerServiceBankAccountDTO vo, Long userId, Long deptId, String operName, Integer isInner);

    void modifyCustomerServiceBankAccount(CustomerServiceBankAccountDTO vo, Long deptId);

    void updatePeriodBankPaymentByCustomerServiceId(Long customerServiceId, Integer updateOrDelete, CustomerServiceBankAccount bankAccount, Long userId, Long deptId, String operName, Integer isInner);

    CustomerServiceFinanceTaxInfoDTO customerServiceFinanceTaxInfo(Long customerServiceId);

    List<CustomerServiceImportDTO> importDatas(List<CustomerServiceImportDTO> data);

    List<CustomerServiceImportDTO> importCustomerServiceTag(List<CustomerServiceImportDTO> data);

    List<CustomerServiceImportDTO> importCustomerServiceTaxCheck(List<CustomerServiceImportDTO> data);

    List<CustomerServiceImportDTO> importCustomerServiceSysAccount(List<CustomerServiceImportDTO> data);

    IPage<CustomerServicePeriodYearDTO> customerServicePeriodYearList(Long deptId, CustomerServicePeriodYearSearchVO vo);

    List<CustomerServicePeriodYearDTO> selectCustomerServicePeriodYearList(Long deptId, CustomerServicePeriodYearSearchVO vo);

    List<CustomerServicePeriodMonth> getCustomerPeriodByListCreditCodeAndPeriod(List<RemoteCreditCodePeriodVO> voList);

    List<String> taxCheckList();

    GetMonthDTO getMonth(Integer diff);

    void modifyAccountingRemark(Long deptId, ModifyRemarkVO vo);

    void modifyAdvisorRemark(Long deptId, ModifyRemarkVO vo);

    void createByNewCustomerTransfer(NewCustomerTransferAllInfoDTO newCustomerTransferAllInfo, String serviceNumber, Long accountingTopDeptId, Long deptId);

    void createByNewCustomerTransferMap(Map<Long, NewCustomerTransferAllInfoDTO> newCustomerTransferAllInfoMap, Long accountingTopDeptId, Long deptId);

    NewCustomerTransferInfoDTO getNewCustomerTransferInfo(Long id);

    List<AddPeriodMonthDTO> importPeriodMonth(List<AddPeriodMonthDTO> data);

    DispatchDeptDTO getCustomerServiceRecentlyAccountingDeptInfo(Long customerServiceId);

    CustomerServiceRestartCheckDTO customerServiceRestartCheck(Long customerServiceId);

    CustomerServiceDispatchPeriodRangeDTO getDispatchPeriodRange(List<Long> ids);

    CommonOperateDTO dispatchAccountingV2(CustomerServiceDispatchV2VO vo);

    void xqySetCustomerStatus(LocalDate date);

    void xqySetCustomerServiceUser(LocalDate date);

    void errorDataNoticeTask();

    CustomerBatchSearchResultDTO customerListByBatchCustomerName(List<String> customerNames);

    List<CCustomerService> getByTaxNumberList(List<String> taxNumberList);

    List<RemoteCustomerBankAccountDTO> getCustomerBankAccountNumberByBankNumbers(RemoteCustomerBankAccountNumberSearchVO vo);

    RemoteCustomerBankAccountDTO getCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(String bankAccountNumber, Long businessTopDeptId);

    void createCustomerBankAccount(RemoteCustomerBankAccountVO vo);

    List<CustomerServiceBankAccount> customerServiceAccountingCashierBankList(Long customerServiceId, Integer period);

    void updateLastInAccountId(Long customerServiceId, Long accountingCashierId, String period);

    void updateLastInAccountIdByDelete(Long customerServiceId, Long accountingCashierId, String period);

    Long selectNoBankCount(UserDeptDTO userDeptDTO, List<Long> queryDeptIds);

    List<CCustomerService> serviceSelectNoDataScope(String keyWord, Long deptId);

    CustomerServiceYearCollectV2DTO customerServiceYearCollectV2(Long customerServiceId, Integer year);

    List<RemoteCustomerPeriodBankDTO> getCustomerPeriodBankList(List<String> bankAccountNumbers);

    List<RemoteCustomerBankAccountDTO> getCustomerBankListByCustomerServiceIds(List<Long> customerServiceIds);

    void syncStatusTask();

    List<CCustomerService> serviceSelectForDeliver(String keyWord, Long deptId);

    List<CommonDeptCountDTO> customerServiceYearAccountingCountList(UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceYearAdvisorCountList(UserDeptDTO userDeptDTO);

    void remoteUpdateCustomerTag(RemoteUpdateCustomerTagVO vo);

    List<RemoteCustomerTagDTO> getCustomerTagsByCustomerServiceIds(List<Long> customerServiceIds);

    IPage<CustomerServiceIncomeInfoDTO> customerServiceIncomeInfo(Long deptId, CustomerServiceIncomeInfoSearchVO vo);

    IPage<CustomerServiceYearIncomeInfoDTO> customerServiceYearIncomeInfo(Long deptId, CustomerServiceYearIncomeInfoSearchVO vo);

    List<CommonDeptCountDTO> customerServiceIncomeAdvisorDeptCount(UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceIncomeAccountingDeptCount(UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceYearIncomeAdvisorDeptCount(UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> customerServiceYearIncomeAccountingDeptCount(UserDeptDTO userDeptDTO);

    void removeColumnBlankTask();

    void removeTagTask(String jobParam);

    IPage<CustomerServiceWaitItemDTO> customerServiceWaitDispatchList(Integer itemType, String keyWord, Long deptId, String tagName, Integer tagIncludeFlag, Integer taxType, Integer pageNum, Integer pageSize);

    PageResult<CustomerServiceXmDTO> customerServiceXmList(UserCustomerSearchVO vo);

    void addMedicalAndSocialTag(boolean hasMedicalTag, boolean hasSocialTag, Long customerServiceId, List<CustomerServicePeriodMonth> periodList, String operName, Long deptId);

    void addMedicalAndSocialTagByIds(boolean hasMedicalTag, boolean hasSocialTag, Long customerServiceId, List<Long> periodIdList, String operName, Long deptId);

    TCommonOperateDTO<CustomerServicePeriodMonth> changePeriodBusinessDept(Long deptId, CustomerServicePeriodMonthChangeDeptVO vo);

    TCommonOperateDTO<CustomerServicePeriodMonth> changePeriodAccountingTopDept(Long deptId, CustomerServicePeriodMonthChangeDeptVO vo);

    TCommonOperateDTO<CustomerServicePeriodMonth> changePeriodAdvisorDept(Long deptId, CustomerServicePeriodMonthChangeDeptVO vo);

    TCommonOperateDTO<CustomerServicePeriodMonth> changePeriodAccountingDept(Long deptId, CustomerServicePeriodMonthChangeDeptVO vo);

    CustomerServiceXmDTO userCustomerById(CommonIdVO vo);

    void updateTaxType(UpdateTaxTypeVO vo, Long deptId);

    RemoteCustomerServicePeriodYearDTO getCustomerServicePeriodYearByTaxNumberAndYear(Long deptId, String taxNumber, Integer year);

    CustomerServicePersonDetailDTO customerServicePersonDetail(Long id, Long deptId);

    CustomerServiceDeliverMattersDTO customerServiceDeliverMatters(Long id, Long deptId);

    void modifyCustomerServiceDeliverMatters(CustomerServiceDeliverMattersVO vo, Long deptId);

    CustomerServiceTaxTypeCheckV2VO customerServiceTaxTypeCheckListV2(Long customerServiceId);

    CustomerServiceOtherMattersDTO customerServiceOtherMatters(Long id, Long deptId);

    void modifyCustomerServiceOtherMatters(CustomerServiceOtherMattersVO vo, Long deptId);

    CustomerServicePeriodMonthInfoDTO customerServicePeriodMonthInfo(Long id, Integer year, Long deptId);

    List<CommonFileVO> getPeriodInAccountFileList(Long customerServicePeriodMonthId);
}
