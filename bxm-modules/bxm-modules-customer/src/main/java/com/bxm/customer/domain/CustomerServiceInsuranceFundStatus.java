package com.bxm.customer.domain;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 客户服务五险一金月状态对象 c_customer_service_insurance_fund_status
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("客户服务五险一金月状态对象")
@Accessors(chain = true)
@TableName("c_customer_service_insurance_fund_status")
public class CustomerServiceInsuranceFundStatus extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 五险一金信息ID */
    @Excel(name = "五险一金信息ID")
    @TableField("insurance_fund_id")
    @ApiModelProperty(value = "五险一金信息ID")
    private Long insuranceFundId;

    /** 月份 */
    @Excel(name = "月份")
    @TableField("month")
    @ApiModelProperty(value = "月份")
    private Integer month;

    /** 状态, 1-已扣款, 2-待申报 */
    @Excel(name = "状态, 1-已扣款, 2-待申报")
    @TableField("status")
    @ApiModelProperty(value = "状态, 1-已扣款, 2-待申报")
    private Integer status;



}
