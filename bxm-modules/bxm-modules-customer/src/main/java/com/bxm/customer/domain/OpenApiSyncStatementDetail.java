package com.bxm.customer.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 医社保个人明细查询记录对象 c_open_api_sync_statement_detail
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
@Data
@ApiModel("医社保个人明细查询记录对象")
@Accessors(chain = true)
@TableName("c_open_api_sync_statement_detail")
public class OpenApiSyncStatementDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 同步记录id */
    @Excel(name = "同步记录id")
    @TableField("syc_record_id")
    @ApiModelProperty(value = "同步记录id")
    private Long sycRecordId;

    /** 同步客户id */
    @Excel(name = "同步客户id")
    @TableField("sync_customer_id")
    @ApiModelProperty(value = "同步客户id")
    private Long syncCustomerId;

    /** 唯一标识符 */
    @Excel(name = "唯一标识符")
    @TableField("uid")
    @ApiModelProperty(value = "唯一标识符")
    private String uid;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private String customerId;

    /** 建账月份 */
    @TableField("accounting_month")
    @ApiModelProperty(value = "建账月份")
    private String accountingMonth;

    /** 缴费所属月份 */
    @TableField("payment_month")
    @ApiModelProperty(value = "缴费所属月份")
    private String paymentMonth;

    /** 个人识别号 */
    @Excel(name = "个人识别号")
    @TableField("personal_id")
    @ApiModelProperty(value = "个人识别号")
    private String personalId;

    /** 姓名 */
    @Excel(name = "姓名")
    @TableField("name")
    @ApiModelProperty(value = "姓名")
    private String name;

    /** 参保人员身份 */
    @Excel(name = "参保人员身份")
    @TableField("insured_identity")
    @ApiModelProperty(value = "参保人员身份")
    private String insuredIdentity;

    /** 累计参保月数 */
    @Excel(name = "累计参保月数")
    @TableField("total_insured_months")
    @ApiModelProperty(value = "累计参保月数")
    private String totalInsuredMonths;

    /** 缴费总金额 */
    @Excel(name = "缴费总金额")
    @TableField("total_payment_amount")
    @ApiModelProperty(value = "缴费总金额")
    private String totalPaymentAmount;

    /** 单位承担金额 */
    @Excel(name = "单位承担金额")
    @TableField("company_bear_amount")
    @ApiModelProperty(value = "单位承担金额")
    private String companyBearAmount;

    /** 个人承担金额 */
    @Excel(name = "个人承担金额")
    @TableField("personal_bear_amount")
    @ApiModelProperty(value = "个人承担金额")
    private String personalBearAmount;

    /** 医疗_个人承担 */
    @Excel(name = "医疗_个人承担")
    @TableField("medical_personal_bear")
    @ApiModelProperty(value = "医疗_个人承担")
    private String medicalPersonalBear;

    /** 养老_个人承担 */
    @Excel(name = "养老_个人承担")
    @TableField("pension_personal_bear")
    @ApiModelProperty(value = "养老_个人承担")
    private String pensionPersonalBear;

    /** 失业_个人承担 */
    @Excel(name = "失业_个人承担")
    @TableField("unemployment_personal_bear")
    @ApiModelProperty(value = "失业_个人承担")
    private String unemploymentPersonalBear;

    /** 生育_个人承担 */
    @Excel(name = "生育_个人承担")
    @TableField("maternity_personal_bear")
    @ApiModelProperty(value = "生育_个人承担")
    private String maternityPersonalBear;

    /** 职工社会医疗保险_个人 */
    @Excel(name = "职工社会医疗保险_个人")
    @TableField("employee_medical_insurance_personal")
    @ApiModelProperty(value = "职工社会医疗保险_个人")
    private String employeeMedicalInsurancePersonal;

    /** 职业年金缴费金额_个人 */
    @Excel(name = "职业年金缴费金额_个人")
    @TableField("occupational_annuity_personal")
    @ApiModelProperty(value = "职业年金缴费金额_个人")
    private String occupationalAnnuityPersonal;

    /** 机关事业单位养老保险缴费金额_个人 */
    @Excel(name = "机关事业单位养老保险缴费金额_个人")
    @TableField("government_agency_pension_personal")
    @ApiModelProperty(value = "机关事业单位养老保险缴费金额_个人")
    private String governmentAgencyPensionPersonal;

    /** 公务员医疗补助缴费金额_个人 */
    @Excel(name = "公务员医疗补助缴费金额_个人")
    @TableField("civil_servant_medical_subsidy_personal")
    @ApiModelProperty(value = "公务员医疗补助缴费金额_个人")
    private String civilServantMedicalSubsidyPersonal;

    /** 大额医疗费用补助缴费金额_个人 */
    @Excel(name = "大额医疗费用补助缴费金额_个人")
    @TableField("major_medical_expense_subsidy_personal")
    @ApiModelProperty(value = "大额医疗费用补助缴费金额_个人")
    private String majorMedicalExpenseSubsidyPersonal;

    /** 长期照护保险缴费金额_个人 */
    @Excel(name = "长期照护保险缴费金额_个人")
    @TableField("long_term_care_insurance_personal")
    @ApiModelProperty(value = "长期照护保险缴费金额_个人")
    private String longTermCareInsurancePersonal;

    /** 医疗_单位承担 */
    @Excel(name = "医疗_单位承担")
    @TableField("medical_company_bear")
    @ApiModelProperty(value = "医疗_单位承担")
    private String medicalCompanyBear;

    /** 养老_单位承担 */
    @Excel(name = "养老_单位承担")
    @TableField("pension_company_bear")
    @ApiModelProperty(value = "养老_单位承担")
    private String pensionCompanyBear;

    /** 工伤_单位承担 */
    @Excel(name = "工伤_单位承担")
    @TableField("injury_company_bear")
    @ApiModelProperty(value = "工伤_单位承担")
    private String injuryCompanyBear;

    /** 失业_单位承担 */
    @Excel(name = "失业_单位承担")
    @TableField("unemployment_company_bear")
    @ApiModelProperty(value = "失业_单位承担")
    private String unemploymentCompanyBear;

    /** 生育_单位承担 */
    @Excel(name = "生育_单位承担")
    @TableField("maternity_company_bear")
    @ApiModelProperty(value = "生育_单位承担")
    private String maternityCompanyBear;

    /** 职工重大疾病医疗补助_单位承担 */
    @Excel(name = "职工重大疾病医疗补助_单位承担")
    @TableField("employee_major_disease_medical_subsidy_company_bear")
    @ApiModelProperty(value = "职工重大疾病医疗补助_单位承担")
    private String employeeMajorDiseaseMedicalSubsidyCompanyBear;

    /** 职工社会医疗保险_单位 */
    @Excel(name = "职工社会医疗保险_单位")
    @TableField("employee_medical_insurance_company")
    @ApiModelProperty(value = "职工社会医疗保险_单位")
    private String employeeMedicalInsuranceCompany;

    /** 职工补充医疗保险_单位 */
    @Excel(name = "职工补充医疗保险_单位")
    @TableField("employee_additional_medical_insurance_company")
    @ApiModelProperty(value = "职工补充医疗保险_单位")
    private String employeeAdditionalMedicalInsuranceCompany;

    /** 职业年金缴费金额_单位 */
    @Excel(name = "职业年金缴费金额_单位")
    @TableField("occupational_annuity_company")
    @ApiModelProperty(value = "职业年金缴费金额_单位")
    private String occupationalAnnuityCompany;

    /** 机关事业单位养老保险缴费金额_单位 */
    @Excel(name = "机关事业单位养老保险缴费金额_单位")
    @TableField("government_agency_pension_company")
    @ApiModelProperty(value = "机关事业单位养老保险缴费金额_单位")
    private String governmentAgencyPensionCompany;

    /** 公务员医疗补助缴费金额_单位 */
    @Excel(name = "公务员医疗补助缴费金额_单位")
    @TableField("civil_servant_medical_subsidy_company")
    @ApiModelProperty(value = "公务员医疗补助缴费金额_单位")
    private String civilServantMedicalSubsidyCompany;

    /** 大额医疗费用补助缴费金额_单位 */
    @Excel(name = "大额医疗费用补助缴费金额_单位")
    @TableField("major_medical_expense_subsidy_company")
    @ApiModelProperty(value = "大额医疗费用补助缴费金额_单位")
    private String majorMedicalExpenseSubsidyCompany;

    /** 职工大额医疗互助保险缴费金额_单位 */
    @Excel(name = "职工大额医疗互助保险缴费金额_单位")
    @TableField("employee_major_medical_mutual_insurance_company")
    @ApiModelProperty(value = "职工大额医疗互助保险缴费金额_单位")
    private String employeeMajorMedicalMutualInsuranceCompany;

    /** 长期照护保险缴费金额_单位 */
    @Excel(name = "长期照护保险缴费金额_单位")
    @TableField("long_term_care_insurance_company")
    @ApiModelProperty(value = "长期照护保险缴费金额_单位")
    private String longTermCareInsuranceCompany;

    /** 职工大病医疗互助保险缴费金额_单位 */
    @Excel(name = "职工大病医疗互助保险缴费金额_单位")
    @TableField("employee_major_disease_mutual_insurance_company")
    @ApiModelProperty(value = "职工大病医疗互助保险缴费金额_单位")
    private String employeeMajorDiseaseMutualInsuranceCompany;

}
