package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 个税申报下载轮询对象 c_open_api_download_deliver_relation
 * 
 * <AUTHOR>
 * @date 2025-03-06
 */
@Data
@ApiModel("个税申报下载轮询对象")
@Accessors(chain = true)
@TableName("c_open_api_download_deliver_relation")
public class OpenApiDownloadDeliverRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 交付单id */
    @Excel(name = "交付单id")
    @TableField("deliver_id")
    @ApiModelProperty(value = "交付单id")
    private Long deliverId;

    /** 下载记录id */
    @Excel(name = "下载记录id")
    @TableField("download_id")
    @ApiModelProperty(value = "下载记录id")
    private String downloadId;

    /** 下载链接 */
    @Excel(name = "下载链接")
    @TableField("dwonload_url")
    @ApiModelProperty(value = "下载链接")
    private String dwonloadUrl;

    /** 下载状态，PENDING-待查询，SUCCESS-成功，FAILED-失败 */
    @Excel(name = "下载状态，PENDING-待查询，SUCCESS-成功，FAILED-失败")
    @TableField("status")
    @ApiModelProperty(value = "下载状态，PENDING-待查询，SUCCESS-成功，FAILED-失败")
    private String status;

}
